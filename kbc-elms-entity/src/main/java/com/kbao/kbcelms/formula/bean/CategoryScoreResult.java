package com.kbao.kbcelms.formula.bean;

import com.kbao.kbcelms.riskMatrix.vo.RiskMatrixDetailVO;

import java.math.BigDecimal;

public class CategoryScoreResult {
    private final RiskMatrixDetailVO.CategoryDetailVO category;
    private final BigDecimal score;

    public CategoryScoreResult(RiskMatrixDetailVO.CategoryDetailVO category, BigDecimal score) {
        this.category = category;
        this.score = score;
    }

    public RiskMatrixDetailVO.CategoryDetailVO getCategory() {
        return category;
    }

    public BigDecimal getScore() {
        return score;
    }
}
