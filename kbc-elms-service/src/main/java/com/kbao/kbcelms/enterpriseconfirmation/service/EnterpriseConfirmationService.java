package com.kbao.kbcelms.enterpriseconfirmation.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.enterpriseconfirmation.entity.EnterpriseConfirmation;
import com.kbao.kbcelms.enterpriseconfirmation.entity.ConfirmationProcessRecord;
import com.kbao.kbcelms.enterpriseconfirmation.dao.EnterpriseConfirmationMapper;
import com.kbao.kbcelms.enterpriseconfirmation.bean.EnterpriseConfirmationSearchVo;
import com.kbao.kbcelms.enterpriseconfirmation.bean.EnterpriseConfirmationListVo;
import com.kbao.kbcelms.enterpriseconfirmation.bean.EnterpriseConfirmationDetailVo;
import com.kbao.kbcelms.enterpriseconfirmation.bean.DuplicateEnterpriseCreatorVo;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.kbcelms.enterprise.base.service.EnterpriseBasicInfoService;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import com.kbao.kbcelms.util.StringUtils;import com.kbao.kbcucs.context.RequestContext;
import com.kbao.kbcucs.user.model.UserInfoResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 企业确权Service
 * @Date 2025-08-20
 */
@Service
public class EnterpriseConfirmationService extends BaseSQLServiceImpl<EnterpriseConfirmation, Integer, EnterpriseConfirmationMapper> {

    @Autowired
    private EnterpriseBasicInfoService enterpriseBasicInfoService;

    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;

    @Autowired
    private ConfirmationProcessRecordService confirmationProcessRecordService;

    /**
     * 分页查询企业确权列表（仅查询重复记录）
     */
    public PageInfo<EnterpriseConfirmationListVo> getConfirmationList(PageRequest<EnterpriseConfirmationSearchVo> pageRequest) {
        // TODO: 获取当前登录用户所在机构信息，后续补充
        String currentLegalCode = ""; // 待补充获取逻辑

        EnterpriseConfirmationSearchVo param = pageRequest.getParam();
        param.setLegalCode(currentLegalCode); // 只查询当前机构的记录

        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<EnterpriseConfirmationListVo> list = mapper.getConfirmationList(param);
        return new PageInfo<>(list);
    }

    /**
     * 获取企业确权详情
     */
    public EnterpriseConfirmationDetailVo getConfirmationDetail(Integer id) {
        EnterpriseConfirmation confirmation = super.selectByPrimaryKey(id);
        if (confirmation == null) {
            throw new BusinessException("企业确权记录不存在");
        }

        EnterpriseConfirmationDetailVo result = new EnterpriseConfirmationDetailVo();

        // 获取企业基本信息
        EnterpriseBasicInfo basicInfo = enterpriseBasicInfoService.findByCreditCode(confirmation.getCreditCode());
        result.setEnterpriseBasicInfo(basicInfo);

        // 获取创建企业时的录入信息
        GenAgentEnterprise agentEnterprise = genAgentEnterpriseService.getByCreditCode(confirmation.getCreditCode());
        result.setGenAgentEnterprise(agentEnterprise);

        // 获取重复企业创建人信息列表
        List<DuplicateEnterpriseCreatorVo> duplicateCreators = mapper.getDuplicateCreators(confirmation.getCreditCode());
        result.setDuplicateCreators(duplicateCreators);

        // 获取处理记录
        List<ConfirmationProcessRecord> processRecords = confirmationProcessRecordService.getByCreditCode(confirmation.getCreditCode());
        result.setProcessRecords(processRecords);

        return result;
    }

    /**
     * 插入企业确权记录并检查重复
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertConfirmationRecord(EnterpriseBasicInfo basicInfo) {
        UserInfoResp userInfo = RequestContext.UserInfoResp.get();

        // 创建确权记录
        EnterpriseConfirmation confirmation = new EnterpriseConfirmation();
        confirmation.setLegalCode(userInfo.getLegalCode());
        confirmation.setCreditCode(basicInfo.getCreditCode());
        confirmation.setEnterpriseName(basicInfo.getName());
        confirmation.setDistrictCode(basicInfo.getDistrictCode());
        confirmation.setCity(basicInfo.getCity());
        confirmation.setStaffScale(basicInfo.getStaffNumRange());
        confirmation.setAnnualIncome(StringUtils.formatAmount(basicInfo.getLatestBusinessIncome()));
        confirmation.setTenantId(userInfo.getTenantId());
        confirmation.setCreateTime(new Date());

        // 检查是否有相同统一信用代码的记录
        List<EnterpriseConfirmation> existingRecords = mapper.getByCreditCode(basicInfo.getCreditCode());

        if (existingRecords.size() > 0) {
            // 标记为重复
            confirmation.setIsDuplicate(1);
            confirmation.setIsProcessed(0);
            // 将已存在的记录也标记为重复
            for (EnterpriseConfirmation existing : existingRecords) {
                existing.setIsDuplicate(1);
                existing.setIsProcessed(0);
                mapper.updateByPrimaryKey(existing);
            }
        } else {
            // 不重复
            confirmation.setIsDuplicate(0);
            confirmation.setIsProcessed(1);
        }
        mapper.insert(confirmation);
    }
}
