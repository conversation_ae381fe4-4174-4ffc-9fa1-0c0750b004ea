package com.kbao.kbcelms.riskMatrix.service;

import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcelms.common.config.ElmsContext;
import com.kbao.kbcelms.common.nosql.service.TenantMongoServiceImpl;
import com.kbao.kbcelms.riskMatrix.dao.RiskMatrixReportDao;
import com.kbao.kbcelms.riskMatrix.model.RiskMatrixReportMongo;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.IDUtils;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 风险矩阵报告MongoDB Service
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Slf4j
@Service
public class RiskMatrixReportService extends TenantMongoServiceImpl<RiskMatrixReportMongo, String, RiskMatrixReportDao> {

    /**
     * 保存风险矩阵报告
     * 
     * @param report 报告实体
     * @return 保存后的报告
     */
    public RiskMatrixReportMongo saveReport(RiskMatrixReportMongo report) {
        try {
            // 设置基本信息
            if (EmptyUtils.isEmpty(report.getId())) {
                report.setId(IDUtils.generateBizId("RMR"));
            }
            
            // 设置创建信息
            Date now = DateUtils.getCurrentDate();
            String userId = ElmsContext.getUser().getUserId();
            String userName = getUserName();
            String tenantId = ElmsContext.getTenantId();
            
            if (EmptyUtils.isEmpty(report.getCreateTime())) {
                report.setCreateTime(now);
                report.setCreatorId(userId);
                report.setCreatorName(userName);
            }
            
            report.setUpdateTime(now);
            report.setUpdaterId(userId);
            report.setUpdaterName(userName);
            report.setTenantId(tenantId);
            
            // 设置默认状态
            if (EmptyUtils.isEmpty(report.getReportStatus())) {
                report.setReportStatus("COMPLETED");
            }
            
            // 设置默认版本号
            if (EmptyUtils.isEmpty(report.getReportVersion())) {
                report.setReportVersion("1.0");
            }
            
            return this.save(report);
            
        } catch (Exception e) {
            log.error("保存风险矩阵报告失败，enterpriseId: {}", report.getEnterpriseId(), e);
            throw new BusinessException("保存风险矩阵报告失败: " + e.getMessage());
        }
    }

    /**
     * 根据企业ID查询风险矩阵报告列表
     * 
     * @param enterpriseId 企业ID
     * @return 报告列表
     */
    public List<RiskMatrixReportMongo> getReportsByEnterpriseId(Long enterpriseId) {
        if (enterpriseId == null || enterpriseId <= 0) {
            throw new BusinessException("企业ID不能为空或无效");
        }
        
        String tenantId = SysLoginUtils.getUser().getTenantId();
        return dao.findByEnterpriseId(enterpriseId, tenantId);
    }

    /**
     * 根据企业ID查询最新的风险矩阵报告
     * 
     * @param enterpriseId 企业ID
     * @return 最新报告
     */
    public RiskMatrixReportMongo getLatestReportByEnterpriseId(Long enterpriseId) {
        
        String tenantId = ElmsContext.getTenantId();
        return dao.findLatestByEnterpriseId(enterpriseId, tenantId);
    }

    /**
     * 根据企业ID和报告状态查询风险矩阵报告列表
     * 
     * @param enterpriseId 企业ID
     * @param reportStatus 报告状态
     * @return 报告列表
     */
    public List<RiskMatrixReportMongo> getReportsByEnterpriseIdAndStatus(Long enterpriseId, String reportStatus) {
        if (enterpriseId == null || enterpriseId <= 0) {
            throw new BusinessException("企业ID不能为空或无效");
        }
        
        String tenantId = SysLoginUtils.getUser().getTenantId();
        return dao.findByEnterpriseIdAndStatus(enterpriseId, reportStatus, tenantId);
    }

    /**
     * 更新报告状态
     * 
     * @param reportId 报告ID
     * @param reportStatus 新状态
     * @return 更新后的报告
     */
    public RiskMatrixReportMongo updateReportStatus(String reportId, String reportStatus) {
        if (EmptyUtils.isEmpty(reportId)) {
            throw new BusinessException("报告ID不能为空");
        }
        
        RiskMatrixReportMongo report = this.findById(reportId);
        if (report == null) {
            throw new BusinessException("报告不存在");
        }
        
        report.setReportStatus(reportStatus);
        report.setUpdateTime(DateUtils.getCurrentDate());
        report.setUpdaterId(SysLoginUtils.getUserId());
        report.setUpdaterName(getUserName());
        
        return this.saveOrUpdate(report);
    }

    /**
     * 统计企业的风险矩阵报告数量
     * 
     * @param enterpriseId 企业ID
     * @return 报告数量
     */
    public long countReportsByEnterpriseId(Long enterpriseId) {
        if (enterpriseId == null || enterpriseId <= 0) {
            throw new BusinessException("企业ID不能为空或无效");
        }
        
        String tenantId = SysLoginUtils.getUser().getTenantId();
        return dao.countByEnterpriseId(enterpriseId, tenantId);
    }

    /**
     * 获取当前用户名称
     * 
     * @return 用户名称
     */
    private String getUserName() {
        try {
            return SysLoginUtils.getUserId();
        } catch (Exception e) {
            log.warn("获取用户名称失败，使用用户ID代替", e);
            return SysLoginUtils.getUserId();
        }
    }
}
