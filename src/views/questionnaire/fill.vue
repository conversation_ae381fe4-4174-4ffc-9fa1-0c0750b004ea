<template>
  <InfoPageLayout
    title="问卷填写"
    subtitle="请认真填写以下问卷，我们将根据您的答案生成风险评估报告"
    icon="el-icon-edit-outline"
    :breadcrumb-items="breadcrumbItems"
    @back="$router.back()"
    v-loading.fullscreen.lock="loading"
    element-loading-text="问卷提交中..."
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(255, 255, 255, 0.9)"
  >
    <!-- 问卷信息卡片 -->
    <InfoCard
      :title="form.title"
      :description="form.description"
      icon="el-icon-document"
      class="questionnaire-info-card"
    >
      <template #extra>
        <div class="questionnaire-meta">
          <InfoItem label="题目数量" :value="form.questions.length + '题'" />
          <InfoItem label="预计用时" :value="estimatedTime" />
        </div>
      </template>
    </InfoCard>

    <!-- 问卷题目卡片 -->
    <InfoCard title="问卷题目" icon="el-icon-edit" class="questions-card">
       <div class="questions-list">
         <div v-for="(q, idx) in form.questions" :key="q.id" class="question-item">
           <div class="question-header">
             <div class="question-number">{{ idx + 1 }}</div>
             <div class="question-info">
               <h4 class="question-title">{{ q.title }}</h4>
               <div class="question-meta">
                 <el-tag
                   :type="getQuestionTypeTag(q.type).type"
                   size="small"
                   class="type-tag"
                 >
                   {{ getQuestionTypeTag(q.type).text }}
                 </el-tag>
                 <el-tag
                   v-if="q.scoreItem"
                   type="success"
                   size="small"
                   class="score-tag"
                 >
                   {{ q.scoreItem }}
                 </el-tag>
               </div>
             </div>
           </div>

           <div class="question-content">
             <!-- 调试信息 -->
             <div v-if="q.options && q.options.length === 0" class="debug-info" style="color: red; margin-bottom: 10px;">
               ⚠️ 该题目没有选项数据
             </div>
             <div v-if="!q.options" class="debug-info" style="color: red; margin-bottom: 10px;">
               ⚠️ 该题目的options字段为空
             </div>

             <!-- 单选题 -->
             <el-radio-group
               v-if="q.type === 'single'"
               v-model="answers[q.id]"
               class="option-group"
             >
               <div
                 v-for="option in q.options"
                 :key="option.id"
                 class="option-item"
               >
                 <el-radio :label="option.id" class="option-radio">
                   <span class="option-text">{{ option.text }}</span>
                   <span class="option-score">{{ option.score }}分</span>
                 </el-radio>
               </div>
             </el-radio-group>

             <!-- 多选题 -->
             <el-checkbox-group
               v-else-if="q.type === 'multi'"
               v-model="answers[q.id]"
               class="option-group"
             >
               <div
                 v-for="option in q.options"
                 :key="option.id"
                 class="option-item"
               >
                 <el-checkbox :label="option.id" class="option-checkbox">
                   <span class="option-text">{{ option.text }}</span>
                   <span class="option-score">{{ option.score }}分</span>
                 </el-checkbox>
               </div>
             </el-checkbox-group>

             <!-- 简答题 -->
             <el-input
               v-else-if="q.type === 'text'"
               v-model="answers[q.id]"
               type="textarea"
               :rows="4"
               placeholder="请详细描述您的想法..."
               class="text-input"
               maxlength="500"
               show-word-limit
             />
           </div>
         </div>
       </div>

       <div class="submit-section">
         <el-button
           type="primary"
           size="large"
           @click="handleSubmit"
           class="submit-btn"
           :loading="loading"
         >
           <i class="el-icon-check"></i>
           提交问卷并生成报告
         </el-button>
         <p class="submit-tip">提交后将自动生成您的风险评估报告</p>
       </div>
     </InfoCard>
   </InfoPageLayout>
</template>

<script>
import { getQuestionnaireDetail, submitQuestionnaireAnswer } from '@/api/questionnaire/management'
import { loadRiskMatrixReport } from '@/api/riskMatrix'
import InfoPageLayout from '@/components/layouts/InfoPageLayout.vue'
import InfoCard from '@/components/layouts/InfoCard.vue'
import InfoItem from '@/components/layouts/InfoItem.vue'

export default {
  name: 'QuestionnaireFill',
  components: {
    InfoPageLayout,
    InfoCard,
    InfoItem
  },
  data() {
    return {
      form: {
        id: '',
        title: '',
        description: '',
        questions: []
      },
      answers: {},
      loading: false
    }
  },
  mounted() {
    this.loadQuestionnaireData()
  },
  computed: {
    estimatedTime() {
      const questionCount = this.form.questions.length
      const estimatedMinutes = Math.ceil(questionCount * 0.5) // 每题约30秒
      return `${estimatedMinutes}分钟`
    },
    breadcrumbItems() {
      return [
        { text: '首页', to: '/', icon: 'el-icon-house' },
        { text: '问卷管理', to: '/questionnaire', icon: 'el-icon-document' },
        { text: '问卷填写', icon: 'el-icon-edit-outline' }
      ]
    }
  },
  methods: {
    async loadQuestionnaireData() {
      this.loading = true
      try {
        const questionnaireId = this.$route.params.id
        if (!questionnaireId) {
          this.$message.error('问卷ID不能为空')
          this.$router.back()
          return
        }

        const response = await getQuestionnaireDetail(questionnaireId)

        if (response) {
          let questions = response.questions || []

          // 数据格式适配
          this.form = {
            id: response.id,
            title: response.title,
            description: response.description,
            enterpriseTypes: response.enterpriseTypeList || [],
            questions: this.transformQuestions(questions)
          }
          // 初始化答案对象
          this.initDefaultAnswers()
        } else {
          this.$message.error('加载问卷数据失败')
        }
      } catch (error) {
        console.error('加载问卷数据失败:', error)
        this.$message.error('加载问卷数据失败')
      } finally {
        this.loading = false
      }
    },

    // 转换后端问题数据格式为前端期望格式
    transformQuestions(backendQuestions) {
      return backendQuestions.map(q => ({
        id: q.id,
        title: q.title,
        type: q.type,
        scoreItem: q.scoreId,
        options: this.transformOptions(q.options || []), // 转换选项格式
        required: q.required === 1
      }))
    },

    // 转换选项数据格式
    transformOptions(backendOptions) {

      const transformedOptions = backendOptions.map((option, index) => {
        const transformed = {
          id: option.id || option.label || index, // 兼容多种ID格式
          text: option.optionText || option.text || '', // 兼容两种字段名
          score: option.score || 0,
          value: option.optionValue || option.value || option.label,
          label: option.label // 保留原始label
        }

        return transformed
      })

      return transformedOptions
    },

    initDefaultAnswers() {
      // 初始化答案对象
      this.form.questions.forEach(q => {
        if (q.type === 'single') {
          this.$set(this.answers, q.id, null)
        } else if (q.type === 'multi') {
          this.$set(this.answers, q.id, [])
        } else {
          this.$set(this.answers, q.id, '')
        }
      })
    },

    getQuestionTypeTag(type) {
      const typeMap = {
        'single': { type: 'primary', text: '单选题' },
        'multi': { type: 'warning', text: '多选题' },
        'text': { type: 'info', text: '简答题' }
      }
      return typeMap[type] || { type: 'default', text: '未知' }
    },

    async handleSubmit() {
      // 验证问卷数据
      if (!this.form.id) {
        this.$message.error('问卷数据异常，请刷新页面重试')
        return
      }

      // 验证必填题目
      const unanswered = this.form.questions.filter(q => {
        // 只验证必填题目
        if (!q.required) return false

        if (q.type === 'single') {
          return !this.answers[q.id]
        } else if (q.type === 'multi') {
          return !this.answers[q.id] || this.answers[q.id].length === 0
        } else {
          return !this.answers[q.id] || this.answers[q.id].trim() === ''
        }
      })

      if (unanswered.length > 0) {
        this.$message.warning(`还有 ${unanswered.length} 道必填题目未完成`)
        return
      }

      this.loading = true
      try {
        // 1. 先提交问卷答案到后端
        await this.submitAnswersToBackend()

        // 2. 调用后端接口生成风险矩阵报告
        // await this.generateRiskMatrixReport()

        this.$message.success('问卷提交成功！风险矩阵报告已生成')
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    // 提交答案到后端
    async submitAnswersToBackend() {
      try {
        // 构造答案数据
        const answers = []
        console.log('提交答案到后端:',this.form.questions)
        this.form.questions.forEach(q => {
          const answer = {
            questionId: q.id,
            questionTitle: q.title,
            answerContent: '',
            optionValue: '',
            score: 0,
            scoreId: q.scoreItem
          }

          if (q.type === 'single') {
            const selectedOptionId = this.answers[q.id]
            if (selectedOptionId) {
              const selectedOption = q.options.find(opt => opt.id === selectedOptionId)
              console.log('选项值内容：',selectedOption)
              if (selectedOption) {
                answer.answerContent = selectedOptionId.toString()
                answer.optionValue = selectedOption.value
                answer.score = selectedOption.score || 0
              }
            }
          } else if (q.type === 'multi') {
            const selectedOptionIds = this.answers[q.id] || []
            if (selectedOptionIds.length > 0) {
              answer.answerContent = selectedOptionIds.join(',')
              answer.score = selectedOptionIds.reduce((total, optionId) => {
                const option = q.options.find(opt => opt.id === optionId)
                return total + (option ? option.score || 0 : 0)
              }, 0)
              answer.optionValue = selectedOption.value.join(',')
            }
          } else if (q.type === 'text') {
            answer.answerContent = this.answers[q.id] || ''
            answer.score = 0 // 简答题暂不计分
          }
          answers.push(answer)
        })

        // 提交数据结构
        const submitData = {
          questionnaireId: this.form.id,
          enterpriseId: this.getCurrentEnterpriseId(), // 需要获取当前企业ID
          enterpriseType: this.form.enterpriseTypes[0], // 需要获取当前企业ID
          answers: answers
        }

        console.log('提交答案数据:', submitData)
        const response = await submitQuestionnaireAnswer(submitData)
        console.log('答案提交响应:', response)

        if (response) {
          console.log('答案提交成功')
        } else {
          throw new Error('答案提交失败')
        }
      } catch (error) {
        console.error('提交答案到后端失败:', error)
        throw error
      }
    },

    // 获取当前企业ID（临时方法，实际应从用户信息或路由参数获取）
    getCurrentEnterpriseId() {
      // 这里需要根据实际业务逻辑获取企业ID
      // 可能从用户登录信息、路由参数或其他地方获取
      return this.$route.params.enterpriseId || -1 // 临时使用默认值
    },

    async generateRiskMatrixReport() {
      try {
        const enterpriseId = this.getCurrentEnterpriseId()

        // 从问卷数据或路由参数获取企业类型
        const enterpriseType = this.$route.params.enterpriseType ||
                             (this.form.enterpriseTypes && this.form.enterpriseTypes[0]) ||
                             null // 让后端自动判断企业类型

        console.log('调用后端生成风险矩阵报告，企业ID:', enterpriseId, '企业类型:', enterpriseType)

        // 调用后端接口生成报告
        const response = await loadRiskMatrixReport(enterpriseId, enterpriseType)

        if (response.code === 200 && response.data) {
          console.log('风险矩阵报告生成成功:', response.data)

          // 跳转到报告页面，传递企业ID
          this.navigateToReport(enterpriseId)
        } else {
          throw new Error(response.message || '生成报告失败')
        }
      } catch (error) {
        console.error('生成风险矩阵报告失败:', error)
        throw error
      }
    },

    navigateToReport(enterpriseId) {
      // 跳转到风险矩阵详情页，传递企业ID
      this.$router.push({
        name: 'riskMatrixDetail',
        params: {
          enterpriseId: enterpriseId.toString()
        }
      })
    }
  }
}
</script>

<style scoped>
.questionnaire-info-card {
  margin-bottom: 24px;
}

.questionnaire-meta {
  display: flex;
  gap: 24px;
  align-items: center;
}

.questions-card {
  margin-bottom: 24px;
}

.questions-list {
  margin-bottom: 32px;
}

.question-item {
  margin-bottom: 32px;
  padding: 24px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.question-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.question-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 16px;
}

.question-number {
  background: #667eea;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.question-info {
  flex: 1;
}

.question-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.question-meta {
  display: flex;
  gap: 8px;
  align-items: center;
}

.type-tag {
  font-size: 12px;
}

.score-tag {
  font-size: 12px;
}

.question-content {
  margin-left: 48px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.3s ease;
  overflow: visible;
  min-height: 60px;
  display: flex;
  align-items: center;
}

.option-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.option-radio,
.option-checkbox {
  width: 100%;
  padding: 16px;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.option-radio /deep/ .el-radio__label,
.option-checkbox /deep/ .el-checkbox__label {
  width: 100%;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding-left: 8px !important;
  line-height: 1.4 !important;
  white-space: normal !important;
}

.option-radio /deep/ .el-radio__input,
.option-checkbox /deep/ .el-checkbox__input {
  margin-right: 8px;
  flex-shrink: 0;
}

.option-text {
  color: #2c3e50;
  font-size: 15px;
  flex: 1;
  margin-right: 12px;
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.option-score {
  color: #667eea;
  font-weight: 600;
  font-size: 14px;
  background: #f0f4ff;
  padding: 6px 10px;
  border-radius: 6px;
  margin-left: 12px;
  flex-shrink: 0;
  min-width: 50px;
  text-align: center;
  border: 1px solid #e1e8ff;
}

.text-input {
  margin-top: 8px;
}

.text-input /deep/ .el-textarea__inner {
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  font-size: 15px;
  line-height: 1.6;
}

.text-input /deep/ .el-textarea__inner:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.submit-section {
  text-align: center;
  padding-top: 32px;
  border-top: 1px solid #e2e8f0;
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 16px 40px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 200px;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.submit-tip {
  margin-top: 12px;
  color: #7f8c8d;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .questionnaire-meta {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .question-item {
    padding: 16px;
  }

  .question-header {
    gap: 12px;
  }

  .question-content {
    margin-left: 0;
    margin-top: 16px;
  }

  .option-radio,
  .option-checkbox {
    padding: 12px;
  }

  .option-radio /deep/ .el-radio__label,
  .option-checkbox /deep/ .el-checkbox__label {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 8px !important;
  }

  .option-text {
    margin-right: 0 !important;
    margin-bottom: 8px;
  }

  .option-score {
    margin-left: 0 !important;
    align-self: flex-end;
  }

  .submit-btn {
    width: 100%;
    min-width: auto;
  }
}

/* 额外的样式修复，确保分数显示正确 */
.option-radio .el-radio__label,
.option-checkbox .el-checkbox__label {
  position: relative !important;
  overflow: visible !important;
  text-overflow: initial !important;
  white-space: normal !important;
}

/* 确保分数标签不被截断 */
.option-score {
  position: relative !important;
  z-index: 1 !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 修复可能的Element UI样式冲突 */
.el-radio__label,
.el-checkbox__label {
  max-width: none !important;
}
</style>
