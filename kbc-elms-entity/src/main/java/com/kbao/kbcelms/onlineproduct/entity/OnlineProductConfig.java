package com.kbao.kbcelms.onlineproduct.entity;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 线上产品配置实体
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(value = "OnlineProductConfig", description = "线上产品配置")
public class OnlineProductConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 行业代码(支持多个,JSON格式存储)
     */
    @ApiModelProperty(value = "行业代码")
    private String industryCode;

    /**
     * 行业名称
     */
    @ApiModelProperty(value = "行业名称")
    private String industryName;

    /**
     * 风险发生概率(高/中/低)
     */
    @ApiModelProperty(value = "风险发生概率")
    private String probability;

    /**
     * 风险影响程度(高/中/低)
     */
    @ApiModelProperty(value = "风险影响程度")
    private String impact;

    /**
     * 风险等级(高/中/低)
     */
    @ApiModelProperty(value = "风险等级")
    private String level;

    /**
     * 险种类型列表(JSON格式存储)
     */
    @ApiModelProperty(value = "险种类型列表")
    private String insuranceTypes;

    /**
     * 是否启用(1-启用,0-禁用)
     */
    @ApiModelProperty(value = "是否启用")
    private Integer enabled;

    /**
     * 描述信息
     */
    @ApiModelProperty(value = "描述信息")
    private String description;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 是否删除(0-未删除,1-已删除)
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;
}
