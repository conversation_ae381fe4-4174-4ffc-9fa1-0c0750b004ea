<template>
  <div class="approval-list">

    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool" class="log-tool"></TableToolTemp>

    <SearchForm :searchForm="initParam" :searchFormTemp="searchFormTemp" @normalSearch="normalSearch"
                @normalResetQuery="normalResetQuery"></SearchForm>

    <el-table :data="tableData" class="dt-table" style="width: 100%" v-hover>
      <el-table-column align="center" prop="companyName" label="保险公司"></el-table-column>
      <el-table-column align="center" prop="productCategory" label="产品大类"></el-table-column>
      <el-table-column align="center" prop="productName" label="产品名称"></el-table-column>
      <el-table-column align="center" label="手续费/经纪费">
        <template slot-scope="scope">
          {{ scope.row.handlingFee }}%
        </template>
      </el-table-column>
      <el-table-column align="center" prop="productSolution" label="产品方案/形态"></el-table-column>
      <el-table-column align="center" prop="reportingAgency" label="上报机构"></el-table-column>
      <el-table-column align="center" prop="businessName" label="业务主体"></el-table-column>
      <el-table-column align="center" prop="reporter" label="上报人"></el-table-column>
      <el-table-column align="center" label="审批状态">
        <template slot-scope="scope">
          {{ scope.row.approvalStatus | getDicItemName("elms.approval.status") }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <el-button class="btn-center" type="text" @click="update(scope.row)">查看</el-button>
          <el-button class="btn-center" type="text" v-if="scope.row.approvalStatus == '0'" @click="approval(scope.row)">审批</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam"
                :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>

  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import * as api from "@/api/roleManagement/index.js";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import {findLegalOrgData} from "@/api/userManagement";

export default {
  name: "inquiryApprovalList",
  data() {
    return {
      toolListProps: {
        toolTitle: "询价产品上报审批",
        toolList: []
      },
      tableData: [],
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          roleName: ""
        }
      },
      searchFormTemp: [
        {
          label: "保险公司",
          name: "roleName",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "上报机构",
          name: "roleType",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "审批状态",
          name: "roleType",
          type: "select",
          width: "200px",
          list:[]
        }
      ],
      total: 0,
      legalList:[]
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    }
  },
  filters: {
    getRoleType(list, roleType) {
      let index = list.findIndex((v) => v.dicItemCode == roleType);
      return index > -1 ? list[index].dicItemName : "-";
    }
  },
  async created() {
    await this.getDicFun();
    this.initData();
  },
  methods: {
    async getDicFun() {
      // 询价公司
      this.searchFormTemp[0].list = await getDicItemList("elms.inquiry.company");

      let legalListTemp = await findLegalOrgData({});

      legalListTemp.forEach(item => {
        this.legalList.push({
          dicItemName: item.orgName,
          dicItemCode: item.orgCode
        });
      });
      // 所属机构
      this.searchFormTemp[1].list = this.legalList;

      this.searchFormTemp[2].list = await getDicItemList("elms.approval.status");
    },
    async initData() {

      this.tableData = [
        {"companyName":"平安财险","productCategory":"企财险","productName":"财产一切险","handlingFee":"15","productSolution":"平安财产一切险产品详情.pdf","reportingAgency":"上海分公司","businessName":"大童经纪","reporter":"上报人","approvalStatus":"1"},
        {"companyName":"平安财险","productCategory":"企财险","productName":"财产一切险","handlingFee":"15","productSolution":"平安财产一切险产品详情.pdf","reportingAgency":"上海分公司","businessName":"大童经纪","reporter":"上报人","approvalStatus":"2"},
        {"companyName":"平安财险","productCategory":"企财险","productName":"财产一切险","handlingFee":"15","productSolution":"平安财产一切险产品详情.pdf","reportingAgency":"上海分公司","businessName":"大童经纪","reporter":"上报人","approvalStatus":"0"},
        {"companyName":"平安财险","productCategory":"企财险","productName":"财产一切险","handlingFee":"15","productSolution":"平安财产一切险产品详情.pdf","reportingAgency":"上海分公司","businessName":"大童经纪","reporter":"上报人","approvalStatus":"2"}
      ];
      this.total = this.tableData.length;

      // this.initParam.param.tenantId = this.$store.state.layoutStore.currentLoginUser.tenantId;
      // let res = await api.getRolePage(this.initParam);
      // if (res) {
      //   this.total = res.total;
      //   this.tableData = [];
      //   if (res.list) {
      //     this.tableData = res.list ? res.list : [{}];
      //   }
      // }
    },
    handleTool(item) {
      if (item.name == "新增角色") {
      }
    },
    closePopup() {
      this.addData = _.cloneDeep(this.$options.data().addData);
      this.showPopup = false;
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate();
      });
    },

    // 搜索
    normalSearch(data) {
      this.initParam = data;
      this.initData();
    },
    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.initData();
    },
    update(row) {
      this.isAdd = false;
      this.addData.roleId = row.id;
      this.addData.roleName = row.roleName;
      this.addData.roleDesc = row.roleDesc;
      this.addData.roleType = row.roleType;
      this.showPopup = true;
    },
    approval(row){
      this.$router.push({
        name:"permission",
        query:{
          roleId:row.id,
          roleName:row.roleName
        }
      })
    },
    async confirm() {
      if (!validateAlls(this.$refs.addForm)){return;}

      let res = await api.saveOrUpdate(this.addData);
      if (res) {
        this.$message({
          type: "success",
          message: this.isAdd ? "新增成功" : "修改成功"
        });
        this.initData();
        this.showPopup = false;
      }
    },
    del(row) {
      this.roleId = row.id;
      this.showDelPopup = true;
    },
    async delHandle() {
      let res = await api.deleteRole({ roleId: this.roleId });
      if (res) {
        this.$message({
          type: "success",
          message: "删除成功"
        });
        this.initData();
      }
      this.showDelPopup = false
    },
    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.initData();
    }
  }
};
</script>
<style lang="less">
.approval-list {
  .nav-list {
    overflow: hidden;
    background-color: #f0f2f5;
    width: 100%;

    .li {
      width: 108px;
      height: 46px;
      background: #ececec;
      border-radius: 6px 6px 0px 0px;
      text-align: center;
      line-height: 46px;
      color: #999;
      font-size: 16px;
      float: left;
      margin-left: 14px;
      cursor: pointer;

      &.active {
        font-size: 18px;
        color: #4f85e6;
        background: #fff;
        border-top: 2px solid #4f85e6;
      }
    }
  }
}

</style>
