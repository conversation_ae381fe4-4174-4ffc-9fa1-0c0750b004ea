<template>
  <div class="enterprise-confirmation-detail">
    <div class="detail-header">
      <el-button @click="goBack" icon="el-icon-arrow-left">返回</el-button>
      <h2>企业确权详情</h2>
    </div>

    <div v-loading="loading">
      <!-- 企业基本信息 -->
      <el-card class="detail-card" shadow="never">
        <div slot="header" class="card-header">
          <span>企业基本信息</span>
        </div>
        <div class="info-grid" v-if="detailData.enterpriseBasicInfo">
          <div class="info-item">
            <label>企业名称：</label>
            <span>{{ detailData.enterpriseBasicInfo.name || '-' }}</span>
          </div>
          <div class="info-item">
            <label>统一信用代码：</label>
            <span>{{ detailData.enterpriseBasicInfo.creditCode || '-' }}</span>
          </div>
          <div class="info-item">
            <label>所在地：</label>
            <span>{{ detailData.enterpriseBasicInfo.city || '-' }}</span>
          </div>
          <div class="info-item">
            <label>人员规模：</label>
            <span>{{ detailData.enterpriseBasicInfo.staffScale || '-' }}</span>
          </div>
          <div class="info-item">
            <label>营业收入：</label>
            <span>{{ detailData.enterpriseBasicInfo.annualIncome || '-' }}</span>
          </div>
          <div class="info-item">
            <label>企业规模：</label>
            <span>{{ detailData.enterpriseBasicInfo.scale || '-' }}</span>
          </div>
        </div>
        <div v-else class="no-data">
          <span>暂无企业基本信息</span>
        </div>
      </el-card>

      <!-- 创建企业时的录入信息 -->
      <el-card class="detail-card" shadow="never">
        <div slot="header" class="card-header">
          <span>创建企业时的录入信息</span>
        </div>
        <div class="info-grid" v-if="detailData.genAgentEnterprise">
          <div class="info-item">
            <label>创建人：</label>
            <span>{{ detailData.genAgentEnterprise.creator || '-' }}</span>
          </div>
          <div class="info-item">
            <label>创建时间：</label>
            <span>{{ formatDateTime(detailData.genAgentEnterprise.createTime) }}</span>
          </div>
          <div class="info-item">
            <label>所属机构：</label>
            <span>{{ detailData.genAgentEnterprise.legalName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>营业部：</label>
            <span>{{ detailData.genAgentEnterprise.salesCenterName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>企业名称：</label>
            <span>{{ detailData.genAgentEnterprise.name || '-' }}</span>
          </div>
          <div class="info-item">
            <label>统一信用代码：</label>
            <span>{{ detailData.genAgentEnterprise.creditCode || '-' }}</span>
          </div>
          <div class="info-item">
            <label>企业类型：</label>
            <span>{{ detailData.genAgentEnterprise.dtType || '-' }}</span>
          </div>
          <div class="info-item">
            <label>企业规模：</label>
            <span>{{ detailData.genAgentEnterprise.enterpriseScale || '-' }}</span>
          </div>
          <div class="info-item">
            <label>人员规模：</label>
            <span>{{ detailData.genAgentEnterprise.staffScale || '-' }}</span>
          </div>
          <div class="info-item">
            <label>所在城市：</label>
            <span>{{ detailData.genAgentEnterprise.city || '-' }}</span>
          </div>
          <div class="info-item">
            <label>营业收入：</label>
            <span>{{ detailData.genAgentEnterprise.annualIncome || '-' }}</span>
          </div>
          <div class="info-item">
            <label>行业分类：</label>
            <span>{{ detailData.genAgentEnterprise.categoryName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>企业联系人：</label>
            <span>{{ detailData.genAgentEnterprise.enterpriseContacter || '-' }}</span>
          </div>
          <div class="info-item">
            <label>联系人电话：</label>
            <span>{{ detailData.genAgentEnterprise.contacterPhone || '-' }}</span>
          </div>
          <div class="info-item">
            <label>备注：</label>
            <span>{{ detailData.genAgentEnterprise.remark || '-' }}</span>
          </div>
          <div class="info-item">
            <label>是否验真：</label>
            <span>{{ detailData.genAgentEnterprise.isVerified === '1' ? '已验真' : '未验真' }}</span>
          </div>
        </div>
        <div v-else class="no-data">
          <span>暂无录入信息</span>
        </div>
      </el-card>

      <!-- 重复企业创建人信息列表 -->
      <el-card class="detail-card" shadow="never">
        <div slot="header" class="card-header">
          <span>重复企业创建人信息</span>
        </div>
        <el-table :data="detailData.duplicateCreators" border v-if="detailData.duplicateCreators && detailData.duplicateCreators.length > 0">
          <el-table-column prop="creator" label="企业创建人" align="center" />
          <el-table-column prop="legalName" label="创建人所在机构" align="center" />
          <el-table-column prop="salesCenterName" label="营业部" align="center" />
          <el-table-column prop="createTime" label="创建时间" align="center">
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="opportunityNum" label="机会数量" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.opportunityNum || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="underwayInsureTypes" label="进行中保险类型" align="center" show-overflow-tooltip />
          <el-table-column prop="lockInsureTypes" label="锁定保险类型" align="center" show-overflow-tooltip />
          <el-table-column prop="issuedInsureTypes" label="已签发保险类型" align="center" show-overflow-tooltip />
        </el-table>
        <div v-else class="no-data">
          <span>暂无重复企业创建人信息</span>
        </div>
      </el-card>

      <!-- 重复企业处理记录列表 -->
      <el-card class="detail-card" shadow="never">
        <div slot="header" class="card-header">
          <span>重复企业处理记录</span>
        </div>
        <el-table :data="detailData.processRecords" border v-if="detailData.processRecords && detailData.processRecords.length > 0">
          <el-table-column prop="userName" label="处理人" align="center" />
          <el-table-column prop="remark" label="批注" align="center" show-overflow-tooltip />
          <el-table-column prop="processTime" label="处理时间" align="center">
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.processTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="legalName" label="处理机构" align="center" />
        </el-table>
        <div v-else class="no-data">
          <span>暂无处理记录</span>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getConfirmationDetail } from '@/api/enterprise/confirmation'

export default {
  name: 'EnterpriseConfirmationDetail',
  data() {
    return {
      loading: false,
      detailData: {
        enterpriseBasicInfo: null,
        genAgentEnterprise: null,
        duplicateCreators: [],
        processRecords: []
      }
    }
  },

  created() {
    this.loadDetail()
  },

  methods: {
    async loadDetail() {
      this.loading = true
      try {
        const id = this.$route.params.id
        const response = await getConfirmationDetail(id)
        this.detailData = response.data || {}
      } catch (error) {
        console.error('加载详情失败:', error)
        this.$message.error('加载详情失败')
      } finally {
        this.loading = false
      }
    },

    goBack() {
      this.$router.go(-1)
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return this.$moment(dateTime).format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>

<style scoped>
.enterprise-confirmation-detail {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.detail-header h2 {
  margin-left: 10px;
  margin: 0;
  color: #333;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: bold;
  color: #333;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: bold;
  min-width: 120px;
  color: #666;
}

.info-item span {
  color: #333;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 40px 0;
}
</style>
