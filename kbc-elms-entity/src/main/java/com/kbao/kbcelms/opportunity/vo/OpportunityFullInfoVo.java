package com.kbao.kbcelms.opportunity.vo;

import cn.hutool.json.JSONObject;import com.kbao.kbcelms.opportunitydetail.vo.OpportunityDetailAddVo;import com.kbao.kbcelms.opportunityfiles.model.OpportunityFiles;import lombok.Data;import java.util.Date;import java.util.List;import java.util.Map;
@Data
public class OpportunityFullInfoVo extends OpportunityAddReqVo {
    private List<OpportunityFiles> fileList;
    private List<ActivityInfo> progressList;

    @Data
    public static class ActivityInfo {
        private String activityId;
        private String activityName;
        private String activityType;
        private Date startTime;
        private Date endTime;
        private Long durationInMillis;
        private String assignee;
        private String taskId;
        private Map<String, Object> customProperties;
        private String status;
    }
}
