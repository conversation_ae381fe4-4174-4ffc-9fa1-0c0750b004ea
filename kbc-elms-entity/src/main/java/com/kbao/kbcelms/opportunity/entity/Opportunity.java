package com.kbao.kbcelms.opportunity.entity;

import java.util.Date;
import javax.validation.constraints.Size;
import com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail;import com.kbao.kbcelms.opportunitydetail.vo.OpportunityDetailVO;import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 机会表
 * <AUTHOR>
 * @date 2025/1/15 16:30
 */
@Data
@ApiModel(description = "机会实体")
public class Opportunity {
    /** 主键 */
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;
    
    /** 机会编码 */
    @ApiModelProperty(value = "机会编码", example = "OPP202501150001")
    private String bizCode;
    
    /** 顾问工号 */
    @ApiModelProperty(value = "顾问工号", example = "AG001")
    private String agentCode;
    
    /** 顾问姓名 */
    @ApiModelProperty(value = "顾问姓名", example = "张三")
    private String agentName;
    
    /** 机会名称 */
    @ApiModelProperty(value = "机会名称", example = "某公司员工福利保险项目")
    private String opportunityName;
    
    /** 关联顾问企业ID */
    @ApiModelProperty(value = "关联顾问企业ID", example = "1001")
    private Integer agentEnterpriseId;
    
    /** 机会类型: 1-员服，2-综合 */
    @ApiModelProperty(value = "机会类型", example = "1", notes = "1-员服，2-综合")
    private String opportunityType;
    
    /** 关联行业编码 */
    @ApiModelProperty(value = "关联行业编码", example = "INS001")
    private String industryCode;
    
    /** 机会状态：0-待提交，1-已提交，2-锁定，3-中止，4-终止 */
    @ApiModelProperty(value = "机会状态", example = "0", notes = "0-待提交，1-已提交，2-锁定，3-中止，4-终止")
    private Integer status;
    
    /** 机会关闭原因类型：1-机会已成交，2-机会推进失败，3-无效机会 */
    @ApiModelProperty(value = "机会关闭原因类型", example = "1", notes = "1-机会已成交，2-机会推进失败，3-无效机会")
    private Integer closeReasonType;
    
    /** 区域中心编码 */
    @ApiModelProperty(value = "区域中心编码", example = "AREA001")
    private String areaCenterCode;
    
    /** 区域中心名称 */
    @ApiModelProperty(value = "区域中心名称", example = "华北区域中心")
    private String areaCenterName;
    
    /** 法人公司编码 */
    @ApiModelProperty(value = "法人公司编码", example = "LEGAL001")
    private String legalCode;
    
    /** 法人公司名称 */
    @ApiModelProperty(value = "法人公司名称", example = "某保险公司")
    private String legalName;
    
    /** 分公司编码 */
    @ApiModelProperty(value = "分公司编码", example = "BRANCH001")
    private String companyCode;
    
    /** 分公司名称 */
    @ApiModelProperty(value = "分公司名称", example = "北京分公司")
    private String companyName;
    
    /** 交易服务中心编码 */
    @ApiModelProperty(value = "交易服务中心编码", example = "TRADE001")
    private String tradingCenterCode;
    
    /** 交易服务中心名称 */
    @ApiModelProperty(value = "交易服务中心名称", example = "北京交易服务中心")
    private String tradingCenterName;
    
    /** 销售服务中心编码 */
    @ApiModelProperty(value = "销售服务中心编码", example = "SALES001")
    private String salesCenterCode;
    
    /** 销售服务中心名称 */
    @ApiModelProperty(value = "销售服务中心名称", example = "北京销售服务中心")
    private String salesCenterName;
    
    /** 流程步骤 */
    @ApiModelProperty(value = "流程步骤", example = "STEP1", notes = "流程步骤长度不能超过5个字符")
    @Size(max = 5, message = "流程步骤长度不能超过5个字符")
    private String processStep;
    
    /** 创建人 当前用户ID */
    @ApiModelProperty(value = "创建人ID", example = "USER001")
    private String createId;
    
    /** 创建时间 */
    @ApiModelProperty(value = "创建时间", example = "2025-01-15 16:30:00")
    private Date createTime;
    
    /** 更新人 */
    @ApiModelProperty(value = "更新人ID", example = "USER001")
    private String updateId;
    
    /** 更新时间 */
    @ApiModelProperty(value = "更新时间", example = "2025-01-15 16:30:00")
    private Date updateTime;
    
    /** 租户ID */
    @ApiModelProperty(value = "租户ID", example = "TENANT001")
    private String tenantId;
    
    /** 当前流程id，对应 opportunity_process 表 id */
    @ApiModelProperty(value = "当前流程ID", example = "1001", notes = "对应 opportunity_process 表 id")
    private Integer currentProcessId;
    
    /** 统筹人员id */
    @ApiModelProperty(value = "统筹人员ID", example = "USER002")
    private String coordinator;
    
    /** 项目经理id */
    @ApiModelProperty(value = "项目经理ID", example = "USER003")
    private String projectManager;
    
    /** 项目归属机构code */
    @ApiModelProperty(value = "项目归属机构编码", example = "ORG001")
    private String projectOrgCode;
    
    /** 项目归属机构名称 */
    @ApiModelProperty(value = "项目归属机构名称", example = "某机构")
    private String projectOrgName;
    
    /** 是否删除 0 未删除  1已删除 */
    @ApiModelProperty(value = "是否删除", example = "0", notes = "0-未删除，1-已删除")
    private Integer isDeleted;
}