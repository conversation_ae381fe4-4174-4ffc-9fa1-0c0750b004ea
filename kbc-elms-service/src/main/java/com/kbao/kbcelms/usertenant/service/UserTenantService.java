package com.kbao.kbcelms.usertenant.service;


import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.user.vo.UserAddVO;
import com.kbao.kbcelms.usertenant.dao.UserTenantMapper;
import com.kbao.kbcelms.usertenant.entity.UserTenant;
import com.kbao.kbcums.appsms.enums.IsDeleteEnum;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;


@Service
public class UserTenantService extends BaseSQLServiceImpl<UserTenant, Integer, UserTenantMapper> {

    public void saveOrUpdate(UserAddVO addVO) {
        UserTenant userTenant = mapper.getByUserIdAndTenantId(addVO.getUserId(), addVO.getTenantId());
        if (EmptyUtils.isEmpty(userTenant)) {
            userTenant = new UserTenant();
            BeanUtils.copyProperties(addVO, userTenant);
            userTenant.setId(null);// addVO中有id字段，该值为用户表主键id
            userTenant.setCreateId(SysLoginUtils.getUserId());
            userTenant.setCreateTime(DateUtils.getCurrentDate());
            userTenant.setIsDeleted(IsDeleteEnum.NO.getCode());
            userTenant.setStatus("1");
            mapper.insert(userTenant);
        } else {
//            userTenant.setRelationType(addVO.getRelationType());
            userTenant.setOrganCode(addVO.getOrganCode());
            userTenant.setOrganCodePath(addVO.getOrganCodePath());
            userTenant.setOrganName(addVO.getOrganName());
            userTenant.setOrganNamePath(addVO.getOrganNamePath());
            userTenant.setWechatAccount(addVO.getWechatAccount());
            userTenant.setExpertType(addVO.getExpertType());
            userTenant.setUpdateId(SysLoginUtils.getUserId());
            userTenant.setUpdateTime(DateUtils.getCurrentDate());
            mapper.updateByPrimaryKey(userTenant);
        }
    }

    public void setUserStatus(UserTenant userTenant) {
        mapper.setUserStatus(userTenant);
    }

    public UserTenant getByUserIdAndTenantId(String userId, String tenantId){
        return mapper.getByUserIdAndTenantId(userId, tenantId);
    }

    public void deleteUserTenant(UserTenant userTenant) {
        mapper.deleteByUserIdAndTenantId(userTenant);
    }

    public void deleteByUserId(UserTenant userTenant) {
        mapper.deleteByUserId(userTenant);
    }

}
