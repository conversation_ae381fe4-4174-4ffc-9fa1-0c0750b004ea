package com.kbao.kbcelms.enterpriseconfirmation.bean;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 企业确权列表VO
 * @Date 2025-08-20
 */
@Data
@ApiModel(description = "企业确权列表VO")
public class EnterpriseConfirmationListVo {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称", example = "某保险经纪有限公司")
    private String enterpriseName;

    /**
     * 社会统一信用代码
     */
    @ApiModelProperty(value = "社会统一信用代码", example = "91110000123456789X")
    private String creditCode;

    /**
     * 所在地
     */
    @ApiModelProperty(value = "所在地", example = "北京市")
    private String city;

    /**
     * 人员规模
     */
    @ApiModelProperty(value = "人员规模", example = "2599-3000人")
    private String staffScale;

    /**
     * 营业收入
     */
    @ApiModelProperty(value = "营业收入", example = "450-500万元")
    private String annualIncome;

    /**
     * 处理状态：0-未处理，1-已处理
     */
    @ApiModelProperty(value = "处理状态", example = "0", notes = "0-未处理，1-已处理")
    private Integer processStatus;

    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间", example = "2025-08-20 16:30:00")
    private Date createTime;
}
