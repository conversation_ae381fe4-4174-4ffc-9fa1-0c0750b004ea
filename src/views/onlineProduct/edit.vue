<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { 
  getIndustryTree, 
  getInsuranceTypeOptions,
  getOnlineProductConfigDetail,
  createOnlineProductConfig,
  updateOnlineProductConfig
} from '@/api/onlineProduct'

export default {
  name: 'OnlineProductConfigEdit',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      isEdit: false,
      isView: false,
      loading: false,
      form: {
        id: '',
        industryCode: [], // 支持多选，初始化为数组
        probability: '',
        impact: '',
        level: '',
        insuranceTypes: [],
        enabled: true,
        description: '',
        createTime: ''
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              { 
                prop: 'industryCode', 
                label: '行业分类', 
                type: 'cascader', 
                placeholder: '请选择行业分类，支持多选和任意级别选择，下级全选时上级自动选中', 
                options: [],                  // 占用两个区域的宽度（8*2=16）
                props: {
                  value: 'value',
                  label: 'label',
                  children: 'children',
                  expandTrigger: 'click',      // 优化：点击展开，避免hover误触
                  emitPath: false,
                  multiple: true,
                  checkStrictly: false        // 修改：关闭严格模式，启用父子级联
                },
                filterable: true,              // 优化：启用搜索过滤
                collapseTags: true,          // 启动标签折叠，让标签自适应显示
                maxCollapseTags: 10,           // 限制显示数量
                showAllLevels: false,         // 优化：不显示完整路径
                disabled: false,
                // 操作提示
                helpText: '💡 操作提示：支持搜索关键词快速定位，可选择不同级别行业，支持多选且标签自适应显示'
              },
              { 
                prop: 'probability', 
                label: '风险发生概率', 
                type: 'radio', 
                options: [
                  { label: '高', value: '高' },
                  { label: '中', value: '中' },
                  { label: '低', value: '低' }
                ]
              },
              { 
                prop: 'impact', 
                label: '风险影响程度', 
                type: 'radio', 
                options: [
                  { label: '高', value: '高' },
                  { label: '中', value: '中' },
                  { label: '低', value: '低' }
                ]
              },
              { 
                prop: 'level', 
                label: '风险等级', 
                type: 'radio', 
                options: [
                  { label: '高', value: '高' },
                  { label: '中', value: '中' },
                  { label: '低', value: '低' }
                ]
              },
              { 
                prop: 'insuranceTypes', 
                label: '险种类型', 
                type: 'select', 
                placeholder: '请选择险种类型', 
                multiple: true,
                options: []
              },
                             { 
                 prop: 'enabled', 
                 label: '是否启用', 
                 type: 'radio',
                 options: [
                   { label: '启用', value: true },
                   { label: '禁用', value: false }
                 ]
               }
            ],
            [
              { 
                prop: 'description', 
                label: '描述信息', 
                type: 'textarea', 
                placeholder: '请输入描述信息', 
                maxlength: 500, 
                rows: 3, 
                showWordLimit: true, 
                span: 24 
              }
            ]
          ]
        }
      ],
      formRules: {
        industryCode: [
          { 
            required: true, 
            message: '请选择行业分类', 
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (!value || (Array.isArray(value) && value.length === 0)) {
                callback(new Error('请选择行业分类'));
              } else {
                callback();
              }
            }
          }
        ],
        probability: [
          { required: true, message: '请选择风险发生概率', trigger: 'change' }
        ],
        impact: [
          { required: true, message: '请选择风险影响程度', trigger: 'change' }
        ],
        level: [
          { required: true, message: '请选择风险等级', trigger: 'change' }
        ],
        insuranceTypes: [
          { required: true, message: '请选择至少一个险种类型', trigger: 'change' }
        ]
      },
      industryOptions: [],
      insuranceTypeOptions: []
    }
  },
  
  computed: {
    // 页面标题
    pageTitle() {
      return this.isView ? '查看产品配置' : (this.isEdit ? '编辑产品配置' : '新增产品配置')
    },
    
    // 页面图标
    pageIcon() {
      return this.isView ? 'el-icon-view' : (this.isEdit ? 'el-icon-edit' : 'el-icon-plus')
    },
    
    // 面包屑导航
    breadcrumbItems() {
      return [
        {
          text: '线上产品配置',
          icon: 'el-icon-goods',
          to: { name: 'onlineProductConfig' }
        },
        {
          text: this.pageTitle,
          icon: this.pageIcon
        }
      ]
    },
    

  },
  
  created() {
    const id = this.$route.params.id
    const mode = this.$route.query.mode
    
    if (id) {
      this.isEdit = mode === 'edit'
      this.isView = mode === 'view'
      this.loadData(id)
    } else {
      // 新增模式 - 确保状态正确
      this.isEdit = false
      this.isView = false
    }
    
    // 加载选项数据
    this.loadOptions()
  },
  
  methods: {
    async loadOptions() {
      try {
        // 并行加载行业树和险种类型选项
        const [industryResponse, insuranceResponse] = await Promise.all([
          getIndustryTree(),
          getInsuranceTypeOptions()
        ])
        
        // 解析响应数据结构
        let industryData = [];
        if (industryResponse && industryResponse.datas && Array.isArray(industryResponse.datas)) {
          industryData = industryResponse.datas;
        } else if (industryResponse && Array.isArray(industryResponse)) {
          industryData = industryResponse;
        } else {
          console.warn('行业树数据格式不符合预期:', industryResponse);
          industryData = [];
        }
        
        this.industryOptions = industryData
        // 更新表单配置中的行业选项（使用树形结构）
        this.updateFieldOptions('industryCode', industryData)
        
        this.insuranceTypeOptions = insuranceResponse
        // 更新表单配置中的险种类型选项
        this.updateFieldOptions('insuranceTypes', insuranceResponse)
      } catch (error) {
        console.error('加载选项数据失败:', error)
        this.$message.error('加载选项数据失败')
      }
    },
    
    async loadData(id) {
      this.loading = true
      try {
        const response = await getOnlineProductConfigDetail(id)
        // 直接使用响应数据
        this.form = { 
          ...response,
          enabled: response.enabled === true || response.enabled === 1,
          createTime: response.createTime || new Date().toLocaleString(),
          // 确保industryCode是数组格式，并处理级联选择的数据结构
          industryCode: this.normalizeIndustryCode(response.industryCode)
        }
        
        // 如果已加载行业选项，更新行业名称显示
        if (this.industryOptions.length > 0 && this.form.industryCode && this.form.industryCode.length > 0) {
          this.form.industryName = this.getIndustryName(this.form.industryCode)
        }
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    async handleSave() {
      try {
        await this.$refs.universalForm.validate()
        this.loading = true
        
        // 处理提交的数据，确保行业代码格式正确
        const submitData = {
          ...this.form,
          industryCode: this.processCascaderData(this.form.industryCode)
        }
        
        let response
        
        if (this.isEdit) {
          // 更新模式
          response = await updateOnlineProductConfig(submitData)
        } else {
          // 新增模式
          response = await createOnlineProductConfig(submitData)
        }
        
        this.$message.success(this.isEdit ? '更新成功' : '创建成功')
        this.handleBack()
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    handleBack() {
      this.$router.push({ name: 'onlineProductConfig' })
    },
    
    handleBreadcrumbClick(item) {
      if (item.to) {
        this.$router.push(item.to)
      }
    },
    
    // 快速清空行业选择
    clearIndustrySelection() {
      this.form.industryCode = []
      this.$message.success('已清空行业选择')
    },
    

    
    findIndustryOption(options, value) {
      for (const option of options) {
        if (String(option.value) === String(value)) {
          return option
        }
        if (option.children && option.children.length > 0) {
          const found = this.findIndustryOption(option.children, value)
          if (found) return found
        }
      }
      return null
    },
    
    // 标准化行业代码数据格式
    normalizeIndustryCode(industryCode) {
      if (!industryCode) return []
      
      // 如果已经是数组格式
      if (Array.isArray(industryCode)) {
        return industryCode
      }
      
      // 如果是字符串格式，尝试解析
      if (typeof industryCode === 'string') {
        try {
          // 尝试解析JSON数组
          if (industryCode.startsWith('[')) {
            return JSON.parse(industryCode)
          } else {
            // 单个值转为数组
            return [industryCode]
          }
        } catch (e) {
          console.warn('解析行业代码失败:', industryCode, e)
          return [industryCode]
        }
      }
      
      return []
    },
    
    // 处理级联选择的数据结构
    processCascaderData(selectedValues) {
      if (!Array.isArray(selectedValues)) return selectedValues
      
      // 过滤掉父级节点，只保留最终选中的叶子节点
      // 当checkStrictly为false时，Element UI会自动处理父子关系
      return selectedValues
    },
    
    // 通用的字段选项更新方法
    updateFieldOptions(fieldProp, options) {
      for (const group of this.formGroups) {
        for (const row of group.fields) {
          const field = row.find(item => item.prop === fieldProp)
          if (field) {
            field.options = options
            return
          }
        }
      }
    },
    
    // 获取行业名称（用于显示）
    getIndustryName(codes) {
      // 支持单个代码或代码数组
      if (!codes) return '';
      
      const codeArray = Array.isArray(codes) ? codes : [codes];
      const flatOptions = this.flattenIndustryTree(this.industryOptions);
      
      const names = codeArray.map(code => {
        const item = flatOptions.find(i => i.value === code);
        return item ? (item.fullName || item.label) : code;
      });
      
      return names.join('，');
    },
    
    // 扁平化行业树结构
    flattenIndustryTree(tree) {
      const result = [];
      const flatten = (nodes) => {
        if (!nodes || !Array.isArray(nodes)) return;
        nodes.forEach(node => {
          result.push({
            label: node.label,
            value: node.value,
            fullName: node.fullName,
            fullPath: node.fullPath,
            level: node.level
          });
          if (node.children && Array.isArray(node.children) && node.children.length > 0) {
            flatten(node.children);
          }
        });
      };
      flatten(tree || []);
      return result;
    }
  }
}
</script>

<style lang="less" scoped>
// 页面样式由EditPageContainer和UniversalForm组件提供
</style> 