<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :loading="loading"
    :is-view="isView"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <!-- 基本信息表单 -->
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="basicFormGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />

    <!-- 风险矩阵配置 -->
    <div class="config-section">
      <DynamicConfigTable
        title="风险矩阵配置"
        subtitle="配置该行业的风险类型、等级和影响程度"
        icon="el-icon-s-grid"
        v-model="form.matrixConfig"
        :default-columns="defaultMatrixColumns"
        :readonly="isView"
        :show-stats="true"
        :show-move-actions="true"
        :validate-on-change="true"
        @change="handleMatrixChange"
        @columns-change="handleMatrixColumnsChange"
      />
    </div>

    <!-- 防控工具配置 -->
    <div class="config-section">
      <DynamicConfigTable
        title="防控工具配置"
        subtitle="配置该行业的风险防控工具和措施"
        icon="el-icon-s-tools"
        v-model="form.toolsConfig"
        :default-columns="defaultToolsColumns"
        :readonly="isView"
        :show-stats="true"
        :show-move-actions="true"
        :validate-on-change="true"
        @change="handleToolsChange"
        @columns-change="handleToolsColumnsChange"
      />
    </div>
  </EditPageContainer>
</template>
<script>
import { getIndustryRiskConfigByCode, updateIndustryRiskConfig, saveIndustryRiskConfig } from '@/api/riskMatrix'
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import DynamicConfigTable from '@/components/layouts/DynamicConfigTable.vue'

export default {
  name: 'IndustryRiskConfig',
  components: { EditPageContainer, UniversalForm, DynamicConfigTable },
  data() {
    return {
      loading: false,
      isView: false,
      form: {
        id: '', // 记录ID，用于编辑模式下的更新操作
        industryLevel1Code: '',
        industryLevel1Name: '',
        industryLevel2Code: '',
        industryLevel2Name: '',
        matrixDesc: '',
        matrixConfig: {
          columnDefs: [],
          rowData: []
        },
        toolsConfig: {
          columnDefs: [],
          rowData: []
        }
      },
      // 默认风险矩阵列配置
      defaultMatrixColumns: [
      ],
      // 默认防控工具列配置
      defaultToolsColumns: [
      ],
      basicFormGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              { prop: 'industryLevel1Code', label: '一级行业编码', type: 'input', placeholder: '请输入一级行业编码', disabled: this.isEditMode },
              { prop: 'industryLevel1Name', label: '一级行业名称', type: 'input', placeholder: '请输入一级行业名称', disabled: this.isEditMode }
            ],
            [
              { prop: 'industryLevel2Code', label: '二级行业编码', type: 'input', placeholder: '请输入二级行业编码（如有）', disabled: this.isEditMode },
              { prop: 'industryLevel2Name', label: '二级行业名称', type: 'input', placeholder: '请输入二级行业名称（如有）', disabled: this.isEditMode }
            ],
            [
              { prop: 'matrixDesc', label: '矩阵说明', type: 'textarea', placeholder: '请输入该行业风险矩阵的核心说明...', maxlength: 500, rows: 4, showWordLimit: true, span: 24 }
            ]
          ]
        }
      ],
      formRules: {
        industryLevel1Code: [{ required: true, message: '请输入一级行业编码', trigger: 'blur' }],
        industryLevel1Name: [{ required: true, message: '请输入一级行业名称', trigger: 'blur' }]
      }
    }
  },
  computed: {
    pageTitle() {
      if (this.isEditMode) {
        return this.form.industryLevel1Name ? `${this.form.industryLevel1Name} - 风险配置` : '编辑行业风险配置'
      } else {
        return '新增行业风险配置'
      }
    },
    pageIcon() {
      return 'el-icon-s-flag'
    },
    isEditMode() {
      // 如果路由中有一级行业编码参数，说明是编辑模式
      return !!this.$route.params.industryLevel1Code
    },
    breadcrumbItems() {
      return [
        { text: '行业风险管理', to: { name: 'industryRisk' }, icon: 'el-icon-s-flag' },
        { text: this.pageTitle, icon: 'el-icon-edit' }
      ]
    }
  },
  created() {
    this.isView = this.$route.query.mode === 'view'
    this.loadIndustryData()
  },
  methods: {
    async loadIndustryData() {
      const industryLevel1Code = this.$route.params.industryLevel1Code

      // 如果没有一级行业编码，说明是新增场景
      if (!industryLevel1Code) {
        this.initializeNewConfig()
        return
      }

      try {
        this.loading = true

        // 调用后端API获取配置数据
        const response = await getIndustryRiskConfigByCode(industryLevel1Code)

        if (response.code === 200 && response.data) {
          const found = response.data

          // 保存记录ID，用于编辑模式下的更新操作
          this.form.id = found.id || ''
          console.log('加载编辑数据：记录ID =', this.form.id)
          this.form.industryLevel1Code = found.industryLevel1Code || ''
          this.form.industryLevel1Name = found.industryLevel1Name || ''
          this.form.industryLevel2Code = found.industryLevel2Code || ''
          this.form.industryLevel2Name = found.industryLevel2Name || ''
          this.form.matrixDesc = found.matrixDesc || ''

          // 加载矩阵配置
          if (found.matrixConfig) {
            this.form.matrixConfig = {
              columnDefs: found.matrixConfig.columnDefs || this.defaultMatrixColumns,
              rowData: found.matrixConfig.rowData || []
            }
          } else if (found.matrix) {
            // 兼容旧数据结构
            this.form.matrixConfig = {
              columnDefs: this.defaultMatrixColumns,
              rowData: found.matrix.map(item => ({
                type: item.type || '',
                level: item.level || '',
                description: item.desc || item.description || '',
                impact: item.impact || '',
                probability: item.probability || ''
              }))
            }
          } else {
            this.form.matrixConfig = {
              columnDefs: this.defaultMatrixColumns,
              rowData: []
            }
          }

          // 加载工具配置
          if (found.toolsConfig) {
            this.form.toolsConfig = {
              columnDefs: found.toolsConfig.columnDefs || this.defaultToolsColumns,
              rowData: found.toolsConfig.rowData || []
            }
          } else if (found.tools) {
            // 兼容旧数据结构
            this.form.toolsConfig = {
              columnDefs: this.defaultToolsColumns,
              rowData: found.tools.map(item => ({
                toolName: item.tool || item.toolName || '',
                applicableRisk: item.risk || item.applicableRisk || '',
                description: item.description || '',
                effect: item.effect || '',
                cost: item.cost || ''
              }))
            }
          } else {
            this.form.toolsConfig = {
              columnDefs: this.defaultToolsColumns,
              rowData: []
            }
          }
        } else {
          // 如果没有找到配置，创建默认配置
          this.$message.warning('未找到行业风险配置数据，将创建新配置')
          this.form.industryName = industryCode || ''
          this.form.industryCode = industryCode || ''
          this.form.matrixDesc = ''
          this.form.matrixConfig = {
            columnDefs: this.defaultMatrixColumns,
            rowData: []
          }
          this.form.toolsConfig = {
            columnDefs: this.defaultToolsColumns,
            rowData: []
          }
        }
      } catch (error) {
        console.error('加载行业风险配置失败:', error)
        this.$message.error('加载配置失败')

        // 设置默认值
        this.form.industryName = industryCode || ''
        this.form.industryCode = industryCode || ''
        this.form.matrixDesc = ''
        this.form.matrixConfig = {
          columnDefs: this.defaultMatrixColumns,
          rowData: []
        }
        this.form.toolsConfig = {
          columnDefs: this.defaultToolsColumns,
          rowData: []
        }
      } finally {
        this.loading = false
      }
    },
    handleBack() {
      this.$router.push({ name: 'industryRisk' })
    },
    handleBreadcrumbClick(item) {
      if (item.to) {
        this.$router.push(item.to)
      }
    },
    async handleSave() {

      try {
        // 验证必填字段
        if (!this.form.industryLevel1Code || !this.form.industryLevel1Code.trim()) {
          this.$message.error('请输入一级行业编码')
          return
        }

        if (!this.form.industryLevel1Name || !this.form.industryLevel1Name.trim()) {
          this.$message.error('请输入一级行业名称')
          return
        }

        // 验证表单
        const valid = await this.$refs.universalForm.validate()
        if (!valid) {
          this.$message.error('请检查表单填写是否正确')
          return
        }

        this.loading = true

        // 判断是新增还是更新
        const isUpdate = this.isEditMode

        // 构建保存数据
        const saveData = {
          industryLevel1Code: this.form.industryLevel1Code ? this.form.industryLevel1Code.trim() : '',
          industryLevel1Name: this.form.industryLevel1Name ? this.form.industryLevel1Name.trim() : '',
          industryLevel2Code: this.form.industryLevel2Code ? this.form.industryLevel2Code.trim() : '',
          industryLevel2Name: this.form.industryLevel2Name ? this.form.industryLevel2Name.trim() : '',
          matrixDesc: this.form.matrixDesc || '',
          riskLevel: this.calculateOverallRiskLevel(),
          status: 'active',
          matrixConfig: this.form.matrixConfig,
          toolsConfig: this.form.toolsConfig,
          changeSummary: isUpdate ? '更新行业风险配置' : '新增行业风险配置'
        }

        // 如果是编辑模式，添加记录ID
        if (isUpdate && this.form.id) {
          saveData.id = this.form.id
          console.log('编辑模式：包含记录ID', this.form.id)
        } else {
          console.log('新增模式：不包含记录ID')
        }

        // 尝试使用新的保存接口
        let response
        try {
          response = await saveIndustryRiskConfig(saveData)
        } catch (error) {
          // 如果新接口失败，回退到旧接口
          console.warn('新接口保存失败，尝试使用旧接口:', error)
          const updateData = {
            matrixConfig: this.form.matrixConfig,
            toolsConfig: this.form.toolsConfig,
            matrixDesc: this.form.matrixDesc,
            // 兼容旧数据结构
            matrix: this.form.matrixConfig.rowData,
            tools: this.form.toolsConfig.rowData
          }
          response = await updateIndustryRiskConfig(this.form.industryCode, updateData)
        }

        if (response.code === 200) {
          this.$message.success('保存成功')
          this.handleBack()
        } else {
          this.$message.error(response.message || '保存失败')
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.loading = false
      }
    },

    // ==================== 新增事件处理方法 ====================

    /**
     * 处理风险矩阵数据变更
     */
    handleMatrixChange(newValue) {
      console.log('风险矩阵数据变更:', newValue)
      // 可以在这里添加自动保存逻辑
    },

    /**
     * 处理风险矩阵列配置变更
     */
    handleMatrixColumnsChange(newColumns) {
      console.log('风险矩阵列配置变更:', newColumns)
      this.$message.success('风险矩阵列配置已更新')
    },

    /**
     * 处理防控工具数据变更
     */
    handleToolsChange(newValue) {
      console.log('防控工具数据变更:', newValue)
      // 可以在这里添加自动保存逻辑
    },

    /**
     * 处理防控工具列配置变更
     */
    handleToolsColumnsChange(newColumns) {
      console.log('防控工具列配置变更:', newColumns)
      this.$message.success('防控工具列配置已更新')
    },

    /**
     * 初始化新增配置
     */
    initializeNewConfig() {
      console.log('初始化新增配置')

      // 设置默认值
      this.form.id = '' // 新增时清空ID
      this.form.industryLevel1Code = ''
      this.form.industryLevel1Name = ''
      this.form.industryLevel2Code = ''
      this.form.industryLevel2Name = ''
      this.form.matrixDesc = ''
      this.form.matrixConfig = {
        columnDefs: this.defaultMatrixColumns,
        rowData: []
      }
      this.form.toolsConfig = {
        columnDefs: this.defaultToolsColumns,
        rowData: []
      }

      this.loading = false
      this.$message.info('请填写行业信息和风险配置')
    },

    /**
     * 计算整体风险等级
     */
    calculateOverallRiskLevel() {
      if (!this.form.matrixConfig || !this.form.matrixConfig.rowData || this.form.matrixConfig.rowData.length === 0) {
        return 'medium'
      }

      const riskCounts = {
        critical: 0,
        high: 0,
        medium: 0,
        low: 0
      }

      // 统计各风险等级数量
      this.form.matrixConfig.rowData.forEach(row => {
        const level = row.level
        if (riskCounts.hasOwnProperty(level)) {
          riskCounts[level]++
        }
      })

      const total = this.form.matrixConfig.rowData.length

      // 根据风险分布计算整体等级
      if (riskCounts.critical > 0 || riskCounts.high / total > 0.5) {
        return 'high'
      } else if (riskCounts.high > 0 || riskCounts.medium / total > 0.6) {
        return 'medium'
      } else {
        return 'low'
      }
    }
  }
}
</script>
<style lang="less" scoped>
.config-section {
  margin-top: 24px;
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  &:first-of-type {
    margin-top: 16px;
  }
}

// 覆盖UniversalForm的样式，使其与配置区域保持一致
/deep/ .universal-form-container {
  .form-group {
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 16px;

    .group-title {
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 1px solid #ebeef5;

      .title-left {
        font-size: 16px;
        font-weight: 600;
        color: #303133;

        i {
          margin-right: 8px;
          color: #FF8030;
          font-size: 18px;
        }
      }
    }
  }
}
</style>
