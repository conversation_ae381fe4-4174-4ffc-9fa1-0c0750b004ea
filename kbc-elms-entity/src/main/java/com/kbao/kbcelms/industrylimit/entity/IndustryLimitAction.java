package com.kbao.kbcelms.industrylimit.entity;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 行业限制执行动作实体
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@ApiModel(value = "IndustryLimitAction", description = "行业限制执行动作")
public class IndustryLimitAction implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 规则ID
     */
    @ApiModelProperty(value = "规则ID")
    private Long ruleId;

    /**
     * 动作类型(applyRule-应用规则,noAction-无动作)
     */
    @ApiModelProperty(value = "动作类型")
    private String type;

    /**
     * 服务流程ID列表(JSON格式)
     */
    @ApiModelProperty(value = "服务流程ID列表")
    private String serviceIds;

    /**
     * 动作描述
     */
    @ApiModelProperty(value = "动作描述")
    private String description;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
}
