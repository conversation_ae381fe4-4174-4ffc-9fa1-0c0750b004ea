package com.kbao.kbcelms.industrylimit.vo;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 行业限制执行动作视图对象
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@ApiModel(value = "IndustryLimitActionVO", description = "行业限制执行动作视图对象")
public class IndustryLimitActionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 动作类型
     */
    @ApiModelProperty(value = "动作类型")
    private String type;

    /**
     * 服务流程ID列表
     */
    @ApiModelProperty(value = "服务流程ID列表")
    private List<String> serviceIds;

    /**
     * 动作描述
     */
    @ApiModelProperty(value = "动作描述")
    private String description;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;
}
