export default [
  {
    path: '/risk-matrix',
    name: 'riskMatrixList',
    component: () => import('@/views/riskMatrix/index.vue'),
    meta: { title: '风险矩阵管理' }
  },
  {
    path: '/risk-matrix/edit/:id?',
    name: 'riskMatrixEdit',
    component: () => import('@/views/riskMatrix/edit.vue'),
    meta: { title: '编辑风险矩阵' }
  },
  {
    path: '/risk-matrix/:matrixId/categories',
    name: 'categoryList',
    component: () => import('@/views/riskMatrix/categoryList.vue'),
    meta: { title: '核心类别管理' }
  },
  {
    path: '/risk-matrix/:matrixId/category/:categoryId?',
    name: 'categoryEdit',
    component: () => import('@/views/riskMatrix/categoryEdit.vue'),
    meta: { title: '编辑核心类别' }
  },
  {
    path: '/risk-matrix/:matrixId/category/:categoryId/levels',
    name: 'levelList',
    component: () => import('@/views/riskMatrix/levelList.vue'),
    meta: { title: '档次配置管理' }
  },
  {
    path: '/risk-matrix/score-item',
    name: 'scoreItemList',
    component: () => import('@/views/riskMatrix/scoreItem.vue'),
    meta: { title: '核心评分项管理' }
  },
  {
    path: '/risk-matrix/score-item/edit/:id?',
    name: 'scoreItemEdit',
    component: () => import('@/views/riskMatrix/scoreItemEdit.vue'),
    meta: { title: '评分项详情' }
  },
  {
    path: '/risk-matrix/detail/:enterpriseId?/:reportId?',
    name: 'riskMatrixDetail',
    component: () => import('@/views/riskMatrix/detail.vue'),
    meta: { title: '风险矩阵分析报告' }
  },
  {
    path: '/risk-matrix/test',
    name: 'riskMatrixTest',
    component: () => import('@/views/riskMatrix/testReport.vue'),
    meta: { title: '风险矩阵报告测试' }
  },
  {
    path: '/risk-matrix/test-radar',
    name: 'riskMatrixTestRadar',
    component: () => import('@/views/riskMatrix/testRadar.vue'),
    meta: { title: '雷达图测试' }
  }
]
