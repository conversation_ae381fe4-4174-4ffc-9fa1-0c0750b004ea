package com.kbao.kbcelms.enterprise.query.config.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询使用统计VO
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ApiModel(value = "QueryUsageStatisticsVO", description = "查询使用统计对象")
public class QueryUsageStatisticsVO {

    @ApiModelProperty(value = "顾问工号")
    private String agentCode;

    @ApiModelProperty(value = "顾问姓名")
    private String agentName;

    @ApiModelProperty(value = "机构名称")
    private String legalName;

    @ApiModelProperty(value = "营业部名称")
    private String salesCenterName;

    @ApiModelProperty(value = "当天使用次数")
    private Long todayUsage;

    @ApiModelProperty(value = "本月使用次数")
    private Long monthUsage;

    @ApiModelProperty(value = "每日限制")
    private Integer dailyLimit;

    @ApiModelProperty(value = "每月限制")
    private Integer monthlyLimit;

    @ApiModelProperty(value = "是否有特殊配置")
    private Boolean isSpecialConfig;
}
