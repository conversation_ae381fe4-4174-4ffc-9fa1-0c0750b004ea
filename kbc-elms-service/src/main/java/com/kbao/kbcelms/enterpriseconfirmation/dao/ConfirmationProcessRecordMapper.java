package com.kbao.kbcelms.enterpriseconfirmation.dao;

import com.kbao.kbcbsc.dao.BaseMapper;
import com.kbao.kbcelms.enterpriseconfirmation.entity.ConfirmationProcessRecord;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 确权处理记录Mapper接口
 * @Date 2025-08-20
 */
public interface ConfirmationProcessRecordMapper extends BaseMapper<ConfirmationProcessRecord, Integer> {

    /**
     * 根据统一信用代码查询处理记录
     */
    List<ConfirmationProcessRecord> getByCreditCode(@Param("creditCode") String creditCode);
}
