package com.kbao.kbcelms.user.service;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.adapter.OrgClientAdapter;
import com.kbao.kbcbsc.adapter.UserClientAdapter;
import com.kbao.kbcbsc.dept.entity.Department;
import com.kbao.kbcbsc.dept.model.DeptIdReq;
import com.kbao.kbcbsc.organization.entity.Organization;
import com.kbao.kbcbsc.organization.model.OrgIdReq;
import com.kbao.kbcbsc.organization.model.OrgTypeReq;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.user.model.UserVo;
import com.kbao.kbcbsc.user.vo.UserFeignRequestVO;
import com.kbao.kbcelms.user.dao.UserMapper;
import com.kbao.kbcelms.user.entity.User;
import com.kbao.kbcelms.user.vo.BranchCoordinatorResponseVO;
import com.kbao.kbcelms.user.vo.ElmsUserFeignRequestVO;
import com.kbao.kbcelms.user.vo.OrgResponseVO;
import com.kbao.kbcelms.user.vo.UserAddVO;
import com.kbao.kbcelms.user.vo.UserInfoVO;
import com.kbao.kbcelms.user.vo.UserRequestVO;
import com.kbao.kbcelms.user.vo.UserResponseVO;
import com.kbao.kbcelms.user.vo.UserRoleAuthVO;
import com.kbao.kbcelms.user.vo.UserRoleVO;
import com.kbao.kbcelms.userorg.entity.UserOrg;
import com.kbao.kbcelms.userorg.service.UserOrgService;
import com.kbao.kbcelms.userorg.vo.UserOrgAddVO;
import com.kbao.kbcelms.userorg.vo.UserOrgResponseVO;
import com.kbao.kbcelms.userorg.vo.UserOrgVO;
import com.kbao.kbcelms.userrole.entity.UserRole;
import com.kbao.kbcelms.userrole.service.UserRoleService;
import com.kbao.kbcelms.userrole.vo.UserRoleRequestVO;
import com.kbao.kbcelms.usertenant.entity.UserTenant;
import com.kbao.kbcelms.usertenant.service.UserTenantService;
import com.kbao.kbcums.appsms.enums.IsDeleteEnum;
import com.kbao.tool.util.CopyUtils;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.MapUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Service
public class UserService extends BaseSQLServiceImpl<User, Integer, UserMapper> {

    private final static String ORG_CONSTANT = "ELMS_ORG_CACHE";

    private final static String DEPT_CONSTANT = "ELMS_DEPT_CACHE";

    private final static String LEGAL_ORG_CONSTANT = "ELMS_LEGAL_ORG_CACHE";

    private final static String COMPANY_ORG_CONSTANT = "COMPANY_LEGAL_ORG_CACHE";

    private final static String USER_INFO_CACHE = "USER_INFO_CACHE";

    private final static long expireTime = 60 * 60 * 24;

    @Autowired
    private UserTenantService userTenantService;

    @Autowired
    private OrgClientAdapter orgClientAdapter;

    @Autowired
    private UserClientAdapter userClientAdapter;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private UserOrgService userOrgService;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 用户列表分页查询
     *
     * @param pageRequest
     * <AUTHOR>
     * @Date 2025/7/22 10:24
     */
    public PageInfo<UserResponseVO> pageUser(PageRequest<UserRequestVO> pageRequest) {
        Map<String, Object> queryParam = MapUtils.objectToMap(pageRequest.getParam());
        if (queryParam == null) {
            queryParam = new ConcurrentHashMap<>(16);
        }
        queryParam.put("tenantId",SysLoginUtils.getUser().getTenantId());

        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        Page<UserResponseVO> page = (Page<UserResponseVO>) this.mapper.findByCondition(queryParam);
        page.getResult().forEach(user -> {
            String phone = user.getPhone();
            if (EmptyUtils.isNotEmpty(phone)) {
                phone = phone.substring(0, 3) + replaceStr(phone.length() - 7) + phone.substring(phone.length() - 4);
                user.setPhone(phone);
            }
        });
        return new PageInfo<>(page);
    }

    /**
     * 修改用户状态
     *
     * @param requestVO
     * <AUTHOR>
     * @Date 2025/7/22 11:16
     */
    public void setUserStatus(UserRequestVO requestVO) {
        UserTenant userTenant = new UserTenant();
        userTenant.setId(requestVO.getUserTenantId());
        userTenant.setStatus(requestVO.getStatus());
        userTenant.setStopReason(requestVO.getStopReason());
        userTenantService.setUserStatus(userTenant);

        User user = new User();
        user.setId(requestVO.getId());
        user.setUpdateId(SysLoginUtils.getUserId());
        user.setUpdateTime(DateUtils.getCurrentDate());
        mapper.updateByPrimaryKeySelective(user);

        // 清除用户缓存信息
        this.clearUserInfoCache(requestVO.getUserId());
    }

    /**
     * 用户详情
     *
     * @param id
     * @param tenantId
     * <AUTHOR>
     * @Date 2025/7/22 10:24
     */
    public UserResponseVO detail(Integer id, String tenantId) {
        Map<String, Object> queryParam = Maps.newHashMap();
        queryParam.put("id", id);
        queryParam.put("tenantId", tenantId);
        return mapper.detail(queryParam);
    }

    /**
     * 删除用户 -- 删除用户租户关联关系
     *
     * @param userId
     * <AUTHOR>
     * @Date 2025/8/7 13:37
     */
    public void deleteUser(String userId) {
        String tenantId = SysLoginUtils.getUser().getTenantId();
        UserTenant userTenant = new UserTenant();
        userTenant.setUserId(userId);
        userTenant.setTenantId(tenantId);
        userTenant.setUpdateId(SysLoginUtils.getUserId());
        userTenant.setUpdateTime(DateUtils.getCurrentDate());
        userTenantService.deleteUserTenant(userTenant);
        // 清除用户缓存信息
        this.clearUserInfoCache(userId);
    }

    /**
     * 根据云服登录账号获取elms用户数据
     *
     * @param tenantId
     * @param bscUserName
     * <AUTHOR>
     * @Date 2025/7/22 10:24
     */
    public UserResponseVO findByBscUserName(String tenantId,String bscUserName) {
        // 校验是否已添加
        User user = mapper.getByBscUserNameAndTenantId(tenantId, bscUserName);
        if (EmptyUtils.isNotEmpty(user)) {
            throw new BusinessException("该用户已存在，请勿重复添加");
        }
        UserResponseVO responseVO = new UserResponseVO();

        user = mapper.findByBscUserName(bscUserName);
        if(EmptyUtils.isNotEmpty(user)) {
            BeanUtils.copyProperties(user, responseVO);
            responseVO.setBscUserName(bscUserName);
//            responseVO.setUserId(user.getUserId());
//            responseVO.setPhone(user.getPhone());
//            responseVO.setEmail(user.getEmail());
//            responseVO.setNickName(user.getNickName());
//            responseVO.setPersonDesc(user.getPersonDesc());
//            responseVO.setEhrUserCode(user.getEhrUserCode());
//            responseVO.setAgentCode(user.getAgentCode());
            return responseVO;
        }

        UserFeignRequestVO requestVO = new UserFeignRequestVO();
        requestVO.setUserName(bscUserName);
        Result<UserVo> result = userClientAdapter.findByUserName(requestVO);
        if (ResultStatusEnum.isSuccess(result.getResp_code()) && EmptyUtils.isNotEmpty(result.getDatas())) {
//            BeanUtils.copyProperties(result.getDatas(), responseVO);
            UserVo userVo = result.getDatas();
            responseVO.setBscUserName(bscUserName);
            responseVO.setUserId(userVo.getUserId());
            responseVO.setPhone(userVo.getPhone());
            responseVO.setEmail(userVo.getEmail());
            responseVO.setNickName(userVo.getNickName());
            return responseVO;
        } else {
            throw new BusinessException("账号错误或者账号在云服不存在");
        }
    }

    /**
     * 用户增改
     *
     * @param addVO
     * <AUTHOR>
     * @Date 2025/7/22 10:24
     */
    public void saveOrUpdate(UserAddVO addVO) {
//        User user = mapper.getByBscUserName(addVO.getTenantId(),addVO.getBscUserName());
        User user = mapper.findByUserId(addVO.getUserId());
        if (EmptyUtils.isEmpty(user)) {
            user = new User();
            BeanUtils.copyProperties(addVO, user);
            user.setBscUseName(addVO.getBscUserName());
            user.setCreateId(SysLoginUtils.getUserId());
            user.setCreateTime(DateUtils.getCurrentDate());
            user.setUpdateId(SysLoginUtils.getUserId());
            user.setUpdateTime(DateUtils.getCurrentDate());
            mapper.insert(user);
        } else {
            addVO.setId(user.getId());
            BeanUtils.copyProperties(addVO, user);
            user.setUpdateId(SysLoginUtils.getUserId());
            user.setUpdateTime(DateUtils.getCurrentDate());
            mapper.updateByPrimaryKeySelective(user);
        }
        // 清除用户缓存
        this.clearUserInfoCache(addVO.getUserId());
        userTenantService.saveOrUpdate(addVO);
    }

    /**
     * 删除用户缓存信息
     *
     * @param userId
     * <AUTHOR>
     * @Date 2025/8/8 10:50
     */
    public void clearUserInfoCache(String userId) {
        String tenantId = SysLoginUtils.getUser().getTenantId();
        String key = redisUtil.generateKey(USER_INFO_CACHE, tenantId, userId);
        redisUtil.del(key);
    }

    /**
     * 获取总部数据
     *
     * @param
     * <AUTHOR>
     * @Date 2025/7/21 16:57
     */
    public List<OrgResponseVO> findDepartmentData() {
        String tenantId = SysLoginUtils.getUser().getTenantId();

        String key = redisUtil.generateKey(tenantId, DEPT_CONSTANT);
        Object value = redisUtil.get(key);
        if (EmptyUtils.isNotEmpty(value)) {
            return (List<OrgResponseVO>) value;
        }

        DeptIdReq deptIdReq = new DeptIdReq();
        deptIdReq.setTenantId(tenantId);
        Result<List<Department>> result = orgClientAdapter.getDeptList(deptIdReq);
        if (ResultStatusEnum.isSuccess(result.getResp_code()) && EmptyUtils.isNotEmpty(result.getDatas())) {

            List<OrgResponseVO> list = new ArrayList<>();

            result.getDatas().forEach(department -> {
                OrgResponseVO responseVO = new OrgResponseVO();
                responseVO.setOrgCode(department.getDeptCode());
                responseVO.setOrgName(department.getDeptName());
                responseVO.setParentCode(department.getParentCode());
                this.transDepartmentData(department.getChildren(), responseVO);
                list.add(responseVO);
            });
            redisUtil.set(key, list, expireTime);
            return list;
        }
        return new ArrayList<>();
    }

    private void transDepartmentData(List<Department> childrens, OrgResponseVO responseVO) {
        if (EmptyUtils.isNotEmpty(childrens)) {
            List<OrgResponseVO> children = new ArrayList<>();

            childrens.forEach(child -> {
                OrgResponseVO childResponseVO = new OrgResponseVO();
                childResponseVO.setOrgCode(child.getDeptCode());
                childResponseVO.setOrgName(child.getDeptName());
                childResponseVO.setParentCode(child.getParentCode());
                this.transDepartmentData(child.getChildren(), childResponseVO);
                children.add(childResponseVO);
            });
            responseVO.setChildren(children);
        }
    }

    /**
     * 获取分公司数据
     *
     * @param
     * <AUTHOR>
     * @Date 2025/7/21 17:06
     */
    public List<OrgResponseVO> findOrganizationData() {

        String tenantId = SysLoginUtils.getUser().getTenantId();

        String key = redisUtil.generateKey(tenantId, ORG_CONSTANT);
        Object value = redisUtil.get(key);
        if (EmptyUtils.isNotEmpty(value)) {
            return (List<OrgResponseVO>) value;
        }

        OrgIdReq orgIdReq = new OrgIdReq();
        orgIdReq.setTenantId(tenantId);
        // 全量机构树结构数据
        Result<List<Organization>> result = orgClientAdapter.getOrgList(orgIdReq);
        if (!ResultStatusEnum.isSuccess(result.getResp_code()) || EmptyUtils.isEmpty(result.getDatas())) {
            return new ArrayList<>();
        }

        List<OrgResponseVO> list = new ArrayList<>();

        // 过滤出法人公司-营业部 这一级数据
        result.getDatas().forEach(org -> {
            this.setOrganizationData(org, list, org.getParentCode());
        });
        // 缓存一天
        redisUtil.set(key, list, expireTime);
        return list;
    }

    /**
     * 查法人机构数据
     *
     * @param
     * <AUTHOR>
     * @Date 2025/7/29 10:21
     */
    public List<OrgResponseVO> findLegalOrgData() {
        String tenantId = SysLoginUtils.getUser().getTenantId();

        String key = redisUtil.generateKey(tenantId, LEGAL_ORG_CONSTANT);
        Object value = redisUtil.get(key);
        if (EmptyUtils.isNotEmpty(value)) {
            return (List<OrgResponseVO>) value;
        }

        OrgTypeReq orgTypeReq = new OrgTypeReq();
        orgTypeReq.setTenantId(tenantId);
        orgTypeReq.setOrgType("2");

        Result<List<Organization>> result = orgClientAdapter.getOrgansByType(orgTypeReq);
        if (!ResultStatusEnum.isSuccess(result.getResp_code()) || EmptyUtils.isEmpty(result.getDatas())) {
            return new ArrayList<>();
        }
        List<OrgResponseVO> list = CopyUtils.copyList(result.getDatas(), OrgResponseVO.class);
        // 缓存一天
        redisUtil.set(key, list, expireTime);
        return list;
    }

    /**
     * 获取分公司数据
     *
     * @param
     * <AUTHOR>
     * @Date 2025/8/12 11:12
     */
    public List<OrgResponseVO> findCompanyOrgData() {
        String tenantId = SysLoginUtils.getUser().getTenantId();

        String key = redisUtil.generateKey(tenantId, COMPANY_ORG_CONSTANT);
        Object value = redisUtil.get(key);
        if (EmptyUtils.isNotEmpty(value)) {
            return (List<OrgResponseVO>) value;
        }

        OrgTypeReq orgTypeReq = new OrgTypeReq();
        orgTypeReq.setTenantId(tenantId);
        orgTypeReq.setOrgType("3");

        Result<List<Organization>> result = orgClientAdapter.getOrgansByType(orgTypeReq);
        if (!ResultStatusEnum.isSuccess(result.getResp_code()) || EmptyUtils.isEmpty(result.getDatas())) {
            return new ArrayList<>();
        }
        List<OrgResponseVO> list = CopyUtils.copyList(result.getDatas(), OrgResponseVO.class);
        // 缓存一天
        redisUtil.set(key, list, expireTime);
        return list;
    }

    /**
     * 根据租户ID获取当前用户配置的机构数据
     *
     * @param tenantId - 前端传入的租户ID
     * <AUTHOR>
     * @Date 2025/8/6 16:06
     */
    public UserOrgResponseVO findLegalOrgDataByTenantId(String tenantId) {

        String userId = SysLoginUtils.getUserId();
        // 获取当前用户配置的机构权限
//        UserTenant userTenant = userTenantService.getByUserIdAndTenantId(userId, tenantId);
        List<UserOrg> orgs = userOrgService.findByUserId(userId, tenantId);
        if(EmptyUtils.isEmpty(orgs)) {
            throw new BusinessException("用户未配置机构权限");
        }
        // 此处租户ID为当前用户登录所在租户ID
//        List<UserRole> userRoles = userRoleService.selectRolesByUserId(userId, SysLoginUtils.getUser().getTenantId());
        UserOrg userOrg = orgs.get(0);

        UserOrgResponseVO responseVO = new UserOrgResponseVO();
        responseVO.setTenantId(tenantId);
        responseVO.setOrgType(userOrg.getOrgType());

        // 如果用户机构权限配置为总公司，返回所有的法人机构数据
        if ("dept".equals(userOrg.getOrgType())) {
            OrgTypeReq orgTypeReq = new OrgTypeReq();
            orgTypeReq.setTenantId(tenantId);
            orgTypeReq.setOrgType("2");
            Result<List<Organization>> result = orgClientAdapter.getOrgansByType(orgTypeReq);
            if (ResultStatusEnum.isSuccess(result.getResp_code()) && EmptyUtils.isNotEmpty(result.getDatas())) {
                List<UserOrgVO> list = CopyUtils.copyList(result.getDatas(), UserOrgVO.class);
                responseVO.setOrgList(list);
            }
        } else {
            // 反之，返回用户已配置机构数据
            List<UserOrg> list = userOrgService.findByUserId(userId, tenantId);
            List<UserOrgVO> orgVOS = new ArrayList<>();
            if(EmptyUtils.isNotEmpty(list)) {
                list.forEach(org -> {
                    UserOrgVO userOrgVO = new UserOrgVO();
                    userOrgVO.setOrgCode(org.getOrganCode());
                    userOrgVO.setOrgName(org.getOrganName());
                    orgVOS.add(userOrgVO);
                });
            }
            responseVO.setOrgList(orgVOS);
        }
        return responseVO;
    }

    /**
     * 过滤营业部，设置营业部父级编码
     *
     * @param org
     * @param list
     * @param parentOrgCode
     * <AUTHOR>
     * @Date 2025/7/21 17:31
     */
    public void setOrganizationData(Organization org, List<OrgResponseVO> list, String parentOrgCode) {
        if ("2".equals(org.getOrgType())) {
            OrgResponseVO responseVO = new OrgResponseVO();
            responseVO.setOrgCode(org.getOrgCode());
            responseVO.setOrgName(org.getOrgName());
            responseVO.setParentCode("");

            List<OrgResponseVO> childs = new ArrayList<>();
            this.getOrgChildren(org.getChildren(), childs, org.getOrgCode());
            responseVO.setChildren(childs);
            list.add(responseVO);
        }
        if (EmptyUtils.isNotEmpty(org.getChildren())) {
            for (Organization child : org.getChildren()) {
                if ("2".equals(child.getOrgType())) {
                    parentOrgCode = child.getOrgCode();
                }
                this.setOrganizationData(child, list, parentOrgCode);
            }
        }
    }

    public void getOrgChildren(List<Organization> childrens, List<OrgResponseVO> childs, String parentOrgCode) {
        if (EmptyUtils.isNotEmpty(childrens)) {
            String orgType = childrens.get(0).getOrgType();
            try {
                if (EmptyUtils.isNotEmpty(orgType) && Integer.parseInt(orgType) > 4) {
                    return;
                }
            } catch (Exception e) {
            }
            if ("4".equals(orgType)) {
                childrens.forEach(child -> {
                    OrgResponseVO childVO = new OrgResponseVO();
                    childVO.setOrgCode(child.getOrgCode());
                    childVO.setOrgName(child.getOrgName());
                    childVO.setParentCode(parentOrgCode);
                    childs.add(childVO);
                });
                return;
            }

            childrens.forEach(child -> {
                this.getOrgChildren(child.getChildren(), childs, parentOrgCode);
            });
        }
    }

    public void getAllOrgChildren(Organization org,List<OrgResponseVO> list,String parentOrgCode) {
        if("4".equals(org.getOrgType())) {
            OrgResponseVO responseVO = new OrgResponseVO();
            responseVO.setOrgCode(org.getOrgCode());
            responseVO.setOrgName(org.getOrgName());
            responseVO.setParentCode(parentOrgCode);
            list.add(responseVO);
        }
    }

    public static String replaceStr(int num) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < num; i++) {
            sb.append("*");
        }
        return sb.toString();
    }
    /**
     * 查询分公司统筹角色人员清单
     * @param roleType 角色性质（必填）
     * @param organCode 机构编码（可为空，为空时查询所有机构）
     * @param nickName 用户姓名（可为空，为空时查询所有用户，不为空时进行模糊搜索）
     * @return 人员清单
     * <AUTHOR>
     * @date 2025/1/15 16:30
     */
    public List<BranchCoordinatorResponseVO> getBranchCoordinatorUsers(int roleType, String organCode, String nickName,String tenantId) {
        List<Map<String, Object>> userMaps = this.getMapper().selectBranchCoordinatorUsers(roleType, organCode, nickName,tenantId);

        List<BranchCoordinatorResponseVO> result = new ArrayList<>();
        for (Map<String, Object> userMap : userMaps) {
            BranchCoordinatorResponseVO responseVO = new BranchCoordinatorResponseVO();
            responseVO.setUserId((String) userMap.get("userId"));
            responseVO.setNickName((String) userMap.get("nickName"));
            responseVO.setUserName((String) userMap.get("userName"));
            responseVO.setRoleType((Integer) userMap.get("roleType"));
            responseVO.setOrganCode((String) userMap.get("organCode"));
            responseVO.setOrganName((String) userMap.get("organName"));
            responseVO.setPhone((String) userMap.get("phone"));
            responseVO.setEmail((String) userMap.get("email"));
            result.add(responseVO);
        }

        return result;
    }

    /**
     * 用户角色分配
     * @param userRoleRequestVO
     * <AUTHOR>
     * @Date 2025/7/28 17:29
     */
    public void addUserRole(UserRoleRequestVO userRoleRequestVO) {
        // 判断是否批量分配角色
        if (EmptyUtils.isNotEmpty(userRoleRequestVO.getRoleIds())) {
            List<UserRole> userRoles = new ArrayList<>();
            userRoleRequestVO.getRoleIds().forEach(roleId -> {
                this.addUserRoleHandle(userRoleRequestVO.getUserId(), roleId);
            });
        } else if (EmptyUtils.isNotEmpty(userRoleRequestVO.getRoleId())) {
            this.addUserRoleHandle(userRoleRequestVO.getUserId(), userRoleRequestVO.getRoleId());
        }
        // 清除用户缓存信息
        this.clearUserInfoCache(userRoleRequestVO.getUserId());
    }

    /**
     * 用户角色新增或状态修改
     *
     * @param userId
     * @param roleId
     * <AUTHOR>
     * @Date 2025/8/1 10:14
     */
    private void addUserRoleHandle(String userId, Integer roleId) {
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("userId", userId);
        queryParam.put("roleId", roleId);
        UserRole userRole = userRoleService.getMapper().findByUserIdAndRoleId(queryParam);
        if (EmptyUtils.isEmpty(userRole)) {
            userRole = new UserRole();
            userRole.setRoleId(roleId);
            userRole.setUserId(userId);
            userRole.setCreateId(SysLoginUtils.getUserId());
            userRole.setCreateTime(DateUtils.getCurrentDate());
            userRole.setIsDeleted(IsDeleteEnum.NO.getCode());
            userRoleService.insert(userRole);
        } else {
            userRole.setIsDeleted(IsDeleteEnum.NO.getCode());
            userRole.setUpdateId(SysLoginUtils.getUserId());
            userRole.setUpdateTime(DateUtils.getCurrentDate());
            userRoleService.updateByPrimaryKeySelective(userRole);
        }
    }

    /**
     * 删除用户角色
     *
     * @param userRoleRequestVO
     * <AUTHOR>
     * @Date 2025/7/28 17:29
     */
    public void deleteUserRole(UserRoleRequestVO userRoleRequestVO) {
        if (EmptyUtils.isNotEmpty(userRoleRequestVO.getRoleIds())) {
            userRoleRequestVO.getRoleIds().forEach(roleId -> {
//                UserRole userRole = new UserRole();
//                userRole.setId(roleId);
//                userRole.setUpdateId(SysLoginUtils.getUserId());
//                userRole.setUpdateTime(DateUtils.getCurrentDate());
//                userRole.setIsDeleted(IsDeleteEnum.YES.getCode());
                userRoleService.deleteByUserIdAndRoleId(userRoleRequestVO.getUserId(), roleId);
//                userRoleService.updateByPrimaryKeySelective(userRole);
            });
        } else if (EmptyUtils.isNotEmpty(userRoleRequestVO.getRoleId())) {
            userRoleService.deleteByUserIdAndRoleId(userRoleRequestVO.getUserId(), userRoleRequestVO.getRoleId());
        }
        // 清除用户缓存信息
        this.clearUserInfoCache(userRoleRequestVO.getUserId());
    }

    /**
     * 用户机构权限增改
     * 
     * @param orgAddVO
     * <AUTHOR>
     * @Date 2025/8/11 15:43
     */
    public void saveOrUpdateUserOrg(UserOrgAddVO orgAddVO) {
        userOrgService.saveOrUpdateUserOrg(orgAddVO);
        // 清除用户缓存信息
        this.clearUserInfoCache(orgAddVO.getUserId());
    }

    /**
     * 获取用户勾选的权限机构
     *
     * @param requestVO
     * <AUTHOR>
     * @Date 2025/7/29 14:49
     */
    public List<String> getCheckedUserOrg(UserRequestVO requestVO) {
        List<UserOrg> list = userOrgService.findByUserId(requestVO.getUserId(), SysLoginUtils.getUser().getTenantId());
        if(EmptyUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
//        List<OrgResponseVO> responseVOS = new ArrayList<>();
//        list.forEach(userOrg -> {
//            OrgResponseVO responseVO = new OrgResponseVO();
//            responseVO.setOrgName(userOrg.getOrganName());
//            responseVO.setOrgCode(userOrg.getOrganCode());
//            responseVOS.add(responseVO);
//        });
//        return responseVOS;
        return list.stream().map(UserOrg::getOrganCode).collect(Collectors.toList());
    }


    /**
     * 根据用户ID查询用户信息
     * @param userId 用户ID
     * @return 用户信息Map，包含nickName、userName等字段
     */
    public User getUserInfoByUserId(String userId) {
        if (EmptyUtils.isEmpty(userId)) {
            return null;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("userId", userId);
        List<User> users = this.selectByParam(param);
        if (EmptyUtils.isEmpty(users)) {
            return null;
        }
        return users.get(0);
    }

    /**
     * 获取该用户角色 - 角色权限
     *
     * @param userId
     * @param tenantId
     * <AUTHOR>
     * @Date 2025/7/30 14:09
     */
    public UserRoleAuthVO getUserRoleAuthByUserId(String userId, String tenantId) {
        UserRoleAuthVO vo = mapper.getUserRoleAuthByUserId(userId, tenantId);
        if (EmptyUtils.isNotEmpty(vo) && EmptyUtils.isNotEmpty(vo.getUserRoles())) {
            List<String> authCodes = new ArrayList<>();
            vo.getUserRoles().forEach(userRole -> {
                userRole.getRoleAuths().forEach(auth -> {
                    authCodes.add(auth.getAuthCode());
                });
            });
            vo.setRoleAuthStr(String.join(",", authCodes));
        }
        return vo;
    }

    /**
     * 获取当前用户归属租户信息
     *
     * @param
     * <AUTHOR>
     * @Date 2025/8/20 9:21
     */
    public UserInfoVO getCurrentUserTenantInfo() {
        String userId = SysLoginUtils.getUserId();
        String tenantId = SysLoginUtils.getUser().getTenantId();

        UserInfoVO userInfoVO = new UserInfoVO();
        userInfoVO.setUserId(userId);
        userInfoVO.setTenantId(tenantId);
        userInfoVO.setUserName(SysLoginUtils.getUser().getUserName());

        // 用户关联租户相关信息
        UserTenant userTenant = userTenantService.getByUserIdAndTenantId(userId, tenantId);
        if (EmptyUtils.isNotEmpty(userTenant)) {
            userInfoVO.setRelationType(userTenant.getRelationType());
            userInfoVO.setOrgCode(userTenant.getOrganCode());
            userInfoVO.setOrgCodePath(userTenant.getOrganCodePath());
            userInfoVO.setOrgName(userTenant.getOrganName());
            userInfoVO.setOrgNamePath(userTenant.getOrganNamePath());
        }
        return userInfoVO;
    }

    /**
     * 获取该用户所属角色
     *
     * @param requestVO
     * <AUTHOR>
     * @Date 2025/7/30 15:57
     */
    public List<UserRoleVO> findRolesByUserId(UserRequestVO requestVO) {
        List<UserRole> userRoles = userRoleService.selectRolesByUserId(requestVO.getUserId(), requestVO.getTenantId());
        List<UserRoleVO> list = new ArrayList<>();
        if (EmptyUtils.isNotEmpty(userRoles)) {
            userRoles.forEach(userRole -> {
                UserRoleVO userRoleVO = new UserRoleVO();
                userRoleVO.setUserRoleId(userRole.getId());
                userRoleVO.setRoleId(userRole.getRoleId());
                list.add(userRoleVO);
            });
        }
        return list;
    }

    /**
     * 获取当前登录用户信息
     *
     * @param
     * <AUTHOR>
     * @Date 2025/8/7 14:04
     */
    public UserInfoVO getCurrentUserInfo() {
        String userId = SysLoginUtils.getUserId();
        String tenantId = SysLoginUtils.getUser().getTenantId();

        String key = redisUtil.generateKey(USER_INFO_CACHE, tenantId, userId);
        Object value = redisUtil.get(key);
        if (EmptyUtils.isNotEmpty(value)) {
            return (UserInfoVO) value;
        }

        UserInfoVO userInfoVO = new UserInfoVO();
        userInfoVO.setUserId(userId);
        userInfoVO.setTenantId(tenantId);

        // 用户角色相关信息
        UserRoleAuthVO vo = mapper.getUserRoleAuthByUserId(userId, tenantId);
        if (EmptyUtils.isNotEmpty(vo)) {
            userInfoVO.setUserRoles(vo.getUserRoles());
            userInfoVO.setUserName(vo.getUserName());
        }

        // 用户关联租户相关信息
        UserTenant userTenant = userTenantService.getByUserIdAndTenantId(userId, tenantId);
        if (EmptyUtils.isNotEmpty(userTenant)) {
            userInfoVO.setRelationType(userTenant.getRelationType());
            userInfoVO.setOrgCode(userTenant.getOrganCode());
            userInfoVO.setOrgCodePath(userTenant.getOrganCodePath());
            userInfoVO.setOrgName(userTenant.getOrganName());
            userInfoVO.setOrgNamePath(userTenant.getOrganNamePath());
        }

        // 用户机构权限相关信息
        List<UserOrg> userOrgs = userOrgService.findByUserId(userId, tenantId);
        if (EmptyUtils.isNotEmpty(userOrgs)) {
            userInfoVO.setOrgType(userOrgs.get(0).getOrgType());
            userInfoVO.setOrgCodes(userOrgs.stream().map(UserOrg::getOrganCode).collect(Collectors.toList()));
            userInfoVO.setOrgNames(userOrgs.stream().map(UserOrg::getOrganName).collect(Collectors.toList()));
        }
        redisUtil.set(key, userInfoVO);
        return userInfoVO;
    }

    /**
     * 云服同步删除elms用户
     *
     * @param request
     * <AUTHOR>
     * @Date 2025/8/14 15:40
     */
    public void syncDeleteElmsUser(ElmsUserFeignRequestVO request) {
        User user = new User();
        user.setUserId(request.getUserId());
        user.setUpdateId(request.getOperatorId());
        user.setUpdateTime(DateUtils.getCurrentDate());
        mapper.deleteByUserId(user);

        UserTenant userTenant = new UserTenant();
        userTenant.setUserId(request.getUserId());
        userTenant.setUpdateId(request.getOperatorId());
        userTenant.setUpdateTime(DateUtils.getCurrentDate());
        userTenantService.deleteByUserId(userTenant);
    }

}
