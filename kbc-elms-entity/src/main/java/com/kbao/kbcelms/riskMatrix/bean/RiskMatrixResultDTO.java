package com.kbao.kbcelms.riskMatrix.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
public class RiskMatrixResultDTO {

    private Long riskMatrixId;

    private String name;

    private List<RiskMatrixLevelVO> riskMatrixLevels;

    private Integer sort;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskMatrixLevelVO {

        /**
         * 类别ID
         */
        private Long categoryId;

        /**
         * 类别名称
         */
        private String categoryName;

        /**
         * 计算方式
         */
        private String calculationMethod;

        /**
         * 计算得分
         */
        private BigDecimal score;

        /**
         * 类别描述
         */
        private String description;

        /**
         * 档次ID
         */
        private Long levelId;

        /**
         * 档次描述
         */
        private String levelDescription;

        /**
         * 分数范围显示
         */
        private String scoreRange;
    }
}
