<template>
  <EditPageContainer
    title="配置题目"
    icon="el-icon-menu"
    :breadcrumb-items="breadcrumbItems"
    :loading="saving"
    :isView="true"
  >
    <!-- 问卷基本信息展示 -->
    <div class="questionnaire-info">
      <InfoCard title="问卷信息" icon="el-icon-document">
        <InfoItem label="问卷标题" :value="questionnaireInfo.title" />
        <InfoItem label="问卷描述" :value="questionnaireInfo.description" />
        <InfoItem label="问卷状态">
            <el-tag
              :type="getStatusTagType(questionnaireInfo.status)"
              size="small"
            >
              {{ getStatusText(questionnaireInfo.status) }}
            </el-tag>
        </InfoItem>
        <InfoItem label="适用企业类型">
            <div v-if="questionnaireInfo.enterpriseTypeList && questionnaireInfo.enterpriseTypeList.length > 0">
              <el-tag
                v-for="type in questionnaireInfo.enterpriseTypeList"
                :key="type"
                size="small"
                class="enterprise-type-tag"
                type="info"
              >
                {{ getEnterpriseTypeText(type) }}
              </el-tag>
            </div>
            <span v-else class="empty-text">全部类型</span>
        </InfoItem>
      </InfoCard>
    </div>

    <!-- 题目配置区域 -->
    <div class="questions-config-section">
      <UniversalTable
        title="题目配置"
        subtitle="配置问卷题目，支持添加、编辑、删除和排序操作"
        title-icon="el-icon-edit-outline"
        :table-data="questions"
        :loading="loading"
        :columns="questionColumns"
        :actions="questionActions"
        :action-column-width="300"
        :show-search-form="false"
        :show-pagination="false"
        add-button-text="添加题目"
        empty-title="暂无题目"
        empty-description="点击添加题目按钮开始创建"
        @add="addQuestion"
        @action-click="handleQuestionAction"
      >
        <!-- 题目类型列插槽 -->
        <template #type="{ row }">
          <el-tag
            :type="getQuestionTypeTagType(row.type)"
            size="small"
          >
            {{ getQuestionTypeText(row.type) }}
          </el-tag>
        </template>

        <!-- 关联评分项列插槽 -->
        <template #scoreItem="{ row }">
          <div class="score-item-display">
            <!-- 使用评分项ID显示对应的名称 -->
            <template v-if="row.scoreItem">
              <el-tag
                size="small"
                type="info"
              >
                {{ getScoreItemName(row.scoreItem) }}
              </el-tag>
            </template>
            <!-- 无关联评分项时的显示 -->
            <span v-else class="empty-text">
              暂无
            </span>
          </div>
        </template>

        <!-- 选项数量列插槽 -->
        <template #optionCount="{ row }">
          <span v-if="row.type === 'text'">-</span>
          <span v-else>{{ row.optionCount || 0 }}个选项</span>
        </template>

        <!-- 排序列插槽 -->
        <template #sortOrder="{ row, index }">
          <div class="sort-column">
            <div class="sort-number">
              {{ row.sortOrder || (index + 1) }}
            </div>
            <div class="sort-controls">
              <el-button
                v-if="index > 0"
                size="mini"
                icon="el-icon-top"
                circle
                @click="moveQuestion(index, 'up')"
              />
              <el-button
                v-if="index < questions.length - 1"
                size="mini"
                icon="el-icon-bottom"
                circle
                @click="moveQuestion(index, 'down')"
              />
            </div>
          </div>
        </template>
      </UniversalTable>
    </div>

    <!-- 题目编辑弹窗 -->
    <el-dialog
      :visible.sync="questionDialogVisible"
      width="900px"
      :close-on-click-modal="false"
      custom-class="question-edit-dialog"
      :show-close="false"
      @close="resetQuestionForm"
    >
      <!-- 自定义弹窗头部 -->
      <div slot="title" class="dialog-header">
        <div class="header-left">
          <div class="header-title-row">
            <i class="el-icon-edit-outline header-icon"></i>
            <h3 class="dialog-title">{{ editingQuestion.id ? '编辑题目' : '添加题目' }}</h3>
          </div>
          <p class="dialog-subtitle">配置题目信息和选项内容</p>
        </div>
        <div class="header-actions">
          <el-button
            size="small"
            icon="el-icon-close"
            circle
            @click="questionDialogVisible = false"
            class="close-btn"
          />
        </div>
      </div>
      <!-- 弹窗内容区域 -->
      <div class="dialog-content">
        <!-- 基本信息区域 -->
        <div class="basic-info-section">
          <div class="section-title">
            <i class="el-icon-document"></i>
            <span>基本信息</span>
          </div>
          <UniversalForm
            ref="questionForm"
            :form-data="editingQuestion"
            :form-rules="questionFormRules"
            :form-groups="questionFormGroups"
            label-width="120px"
          >
          </UniversalForm>
        </div>

        <!-- 选项配置（仅选择题显示） -->
        <div v-if="editingQuestion.type !== 'text'" class="options-section">
          <div class="section-title-with-action">
            <div class="section-title">
              <i class="el-icon-menu"></i>
              <span>选项配置</span>
              <div class="title-badge">
                <span>{{ getQuestionTypeText(editingQuestion.type) }}</span>
              </div>
            </div>
            <el-button size="small" type="primary" @click="addOption" class="add-option-btn">
              <i class="el-icon-plus"></i>
              添加选项
            </el-button>
          </div>

        <!-- 选项表头 -->
        <div class="options-header">
          <div class="header-item index-header">序号</div>
          <div class="header-item content-header">内容</div>
          <div class="header-item score-header">分数</div>
          <div class="header-item action-header">操作</div>
        </div>

        <div class="options-list">
          <div
            v-for="(option, index) in editingQuestion.options"
            :key="option.id || index"
            class="option-item"
          >
            <div class="option-content">
              <div class="option-index">
                <span class="index-badge">{{ getOptionLabel(index) }}</span>
              </div>
              <el-input
                v-model="option.text"
                placeholder="请输入选项内容"
                class="option-text-input"
              />
              <el-input-number
                v-model="option.score"
                :min="0"
                :max="100"
                placeholder="分值"
                class="option-score-input"
                controls-position="right"
              />
              <div class="option-actions">
                <el-tooltip content="删除选项" placement="top">
                  <el-button
                    size="small"
                    type="danger"
                    icon="el-icon-delete"
                    circle
                    @click="removeOption(index)"
                  />
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态提示 -->
        <div v-if="editingQuestion.options.length === 0" class="empty-options">
          <i class="el-icon-info"></i>
          <span>暂无选项，点击上方"添加选项"按钮开始创建</span>
        </div>
      </div>

      <!-- 自定义弹窗底部 -->
      <div slot="footer" class="dialog-footer">
        <div class="footer-content">
          <div class="footer-info">
            <i class="el-icon-info"></i>
            <span v-if="editingQuestion.type !== 'text'">
              已配置 {{ editingQuestion.options.length }} 个选项
            </span>
            <span v-else>简答题无需配置选项</span>
          </div>
          <div class="footer-actions">
            <el-button @click="questionDialogVisible = false" class="cancel-btn">
              <i class="el-icon-close"></i>
              取消
            </el-button>
            <el-button type="primary" @click="saveQuestion" class="save-btn">
              <i class="el-icon-check"></i>
              {{ editingQuestion.id ? '更新题目' : '保存题目' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
    </el-dialog>

    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="删除确认"
      message="删除后无法恢复，请确认是否删除该题目？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmDeleteQuestion"
    />
  </EditPageContainer>
</template>

<script>
import {
  getQuestionOptions,
  getQuestionnaire,
  saveQuestion as saveQuestionApi,
  updateQuestion as updateQuestionApi,
  deleteQuestion as deleteQuestionApi,
  updateQuestionSort
} from '@/api/questionnaire/management'
import { getScoreItemOptions } from '@/api/riskMatrix'
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import InfoCard from '@/components/layouts/InfoCard.vue'
import InfoItem from '@/components/layouts/InfoItem.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'

export default {
  name: 'QuestionConfig',
  components: {
    EditPageContainer,
    UniversalTable,
    UniversalForm,
    InfoCard,
    InfoItem,
    ConfirmDialog
  },
  data() {
    return {
      loading: false,
      saving: false,
      questionnaireInfo: {
        id: null,
        title: '',
        description: '',
        status: 0,
        enterpriseTypeList: [] // 企业类型列表
      },
      questions: [],
      questionDialogVisible: false,
      editingQuestion: {
        id: null,
        type: 'single',
        title: '',
        scoreItem: '', // 关联评分项ID
        options: []
      },
      deleteQuestionIndex: -1,
      scoreItemOptions: [] // 评分项选项列表
    }
  },
  computed: {
    breadcrumbItems() {
      return [
        { text: '问卷管理', to: '/questionnaire' },
        { text: '配置题目' }
      ]
    },
    questionColumns() {
      return [
        { prop: 'title', label: '题目内容', minWidth: 300 },
        { prop: 'type', label: '题目类型', width: 120, slot: true, align: 'center' },
        { prop: 'scoreItem', label: '关联评分项', width: 200, slot: true, align: 'center' },
        { prop: 'optionCount', label: '选项数量', width: 120, slot: true, align: 'center' }
      ]
    },
    questionActions() {
      return [
        { key: 'edit', label: '编辑', icon: 'el-icon-edit', class: 'edit-btn', size: 'mini' },
        { key: 'delete', label: '删除', icon: 'el-icon-delete', class: 'delete-btn', size: 'mini' },
        { key: 'moveUp', label: '上移', icon: 'el-icon-top', class: 'move-up-btn', size: 'mini' },
        { key: 'moveDown', label: '下移', icon: 'el-icon-bottom', class: 'move-down-btn', size: 'mini' }
      ]
    },
    questionFormRules() {
      return {
        type: [{ required: true, message: '请选择题型', trigger: 'change' }],
        title: [{ required: true, message: '请输入题目内容', trigger: 'blur' }]
      }
    },
    questionFormGroups() {
      return [
        {
          title: '题目信息',
          fields: [
            [
              {
                prop: 'type',
                label: '题型',
                type: 'select',
                options: [
                  { value: 'single', label: '单选题' },
                  { value: 'multi', label: '多选题' },
                  { value: 'text', label: '简答题' }
                ],
                required: true
              }
            ],
            [
              {
                type: 'select',
                prop: 'scoreItem',
                label: '关联评分项',
                placeholder: '请选择关联评分项',
                multiple: false,
                filterable: true,
                options: this.scoreItemOptions,
                span: 24
              }
            ],
            [
              {
                prop: 'title',
                label: '题目内容',
                type: 'textarea',
                placeholder: '请输入题目内容',
                required: true
              }
            ]
          ]
        }
      ]
    }
  },
  async created() {
    await this.loadQuestionnaireData()
    await this.loadScoreItemOptions()
  },
  methods: {
    // 加载评分项选项
    async loadScoreItemOptions() {
      try {
        const response = await getScoreItemOptions({
          enterpriseTypes: this.questionnaireInfo.enterpriseTypeList.join(',')
        })

        // 处理不同的返回格式
        let scoreItems = []
        if (response) {
          scoreItems = response
        } else {
          scoreItems = []
        }
        this.scoreItemOptions = scoreItems.map(item => ({
          label: item.name,
          value: item.id
        }))
      } catch (error) {
        console.error('加载评分项选项失败:', error)
        this.$message.error('加载评分项选项失败')
        this.scoreItemOptions = []
      }
    },

    // 加载问卷数据
    async loadQuestionnaireData() {
      const questionnaireId = this.$route.params.id
      if (!questionnaireId) {
        this.$message.error('问卷ID不能为空')
        this.$router.back()
        return
      }

      this.loading = true
      try {
        // const response = await getQuestionsByQuestionnaireId(questionnaireId)
        const response = await getQuestionnaire(questionnaireId)
        if (response) {

          this.questionnaireInfo = {
            id: response.id,
            title: response.title,
            description: response.description,
            status: response.status,
            enterpriseTypeList: response.enterpriseTypeList || []
          }

          this.questions = (response.questions || []).map(q => ({
            id: q.id,
            type: q.type,
            title: q.title,
            scoreItem: q.scoreId || '',
            optionCount: q.optionCount || 0,
            sortOrder: q.sortOrder || 0,
            options: (q.options || []).map(opt => ({
              id: opt.id,
              text: opt.optionText || opt.text,
              score: opt.score || 0
            }))
          }))
        }
      } catch (error) {
        console.error('加载问卷数据失败:', error)
        this.$message.error('加载问卷数据失败')
      } finally {
        this.loading = false
      }
    },

    // 添加题目
    addQuestion() {
      this.editingQuestion = {
        id: null,
        type: 'single',
        title: '',
        scoreItem: '',
        optionCount: 0,
        options: []
      }
      this.questionDialogVisible = true
    },

    // 编辑题目
    async editQuestion(question, index) {
      this.editingQuestion = {
        ...JSON.parse(JSON.stringify(question)),
        _index: index
      }

      // 如果题目有ID且不是文本题，则查询选项进行回显
      if (question.id && question.type !== 'text') {
        try {
          const response = await getQuestionOptions(question.id)
          if (response && response.length > 0) {
            this.editingQuestion.options = response.map(opt => ({
              id: opt.id,
              text: opt.optionText || opt.text,
              score: opt.score || 0
            }))
            this.editingQuestion.optionCount = this.editingQuestion.options.length
          }
        } catch (error) {
          console.error('查询题目选项失败:', error)
          this.$message.warning('查询题目选项失败，将使用默认选项')
        }
      }

      this.questionDialogVisible = true
    },

    // 删除题目
    deleteQuestion(index) {
      this.deleteQuestionIndex = index
      this.$refs.confirmDialog.show()
    },

    // 确认删除题目
    async confirmDeleteQuestion() {
      if (this.deleteQuestionIndex >= 0) {
        const question = this.questions[this.deleteQuestionIndex]

        try {
          // 如果题目有真实ID，调用后端删除接口
          if (question.id && question.id < Date.now() - 1000000) {
            this.loading = true
            await deleteQuestionApi(this.questionnaireInfo.id, question.id)
          }

          // 从本地数组中删除
          this.questions.splice(this.deleteQuestionIndex, 1)

          // 重新排序
          this.updateQuestionSortOrder()

          this.$message.success('题目删除成功')
        } catch (error) {
          console.error('删除题目失败:', error)
          if (error.response && error.response.data && error.response.data.message) {
            this.$message.error(`删除失败：${error.response.data.message}`)
          } else {
            this.$message.error('删除失败，请重试')
          }
        } finally {
          this.loading = false
          this.deleteQuestionIndex = -1
        }
      }
    },

    // 移动题目位置
    async moveQuestion(index, direction) {
      const newIndex = direction === 'up' ? index - 1 : index + 1
      if (newIndex >= 0 && newIndex < this.questions.length) {
        try {
          this.loading = true

          // 交换位置
          const temp = this.questions[index]
          this.$set(this.questions, index, this.questions[newIndex])
          this.$set(this.questions, newIndex, temp)

          // 更新排序号
          this.updateQuestionSortOrder()

          // 调用后端接口更新排序
          const sortData = this.questions.map((question, idx) => ({
            id: question.id && question.id < Date.now() - 1000000 ? question.id : null,
            sortOrder: idx + 1
          })).filter(item => item.id) // 只发送有真实ID的题目

          if (sortData.length > 0) {
            await updateQuestionSort(this.questionnaireInfo.id, { questions: sortData })
          }

          this.$message.success('题目顺序调整成功')
        } catch (error) {
          console.error('调整题目顺序失败:', error)
          // 如果失败，恢复原来的顺序
          const temp = this.questions[newIndex]
          this.$set(this.questions, newIndex, this.questions[index])
          this.$set(this.questions, index, temp)
          this.updateQuestionSortOrder()

          if (error.response && error.response.data && error.response.data.message) {
            this.$message.error(`调整失败：${error.response.data.message}`)
          } else {
            this.$message.error('调整失败，请重试')
          }
        } finally {
          this.loading = false
        }
      }
    },

    // 更新题目排序号
    updateQuestionSortOrder() {
      this.questions.forEach((question, index) => {
        this.$set(question, 'sortOrder', index + 1)
      })
    },

    // 题目操作处理
    handleQuestionAction(eventData) {
      const { action, row, index } = eventData
      switch (action) {
        case 'edit':
          this.editQuestion(row, index)
          break
        case 'delete':
          this.deleteQuestion(index)
          break
        case 'moveUp':
          if (index > 0) {
            this.moveQuestion(index, 'up')
          }
          break
        case 'moveDown':
          if (index < this.questions.length - 1) {
            this.moveQuestion(index, 'down')
          }
          break
      }
    },

    // 保存题目
    async saveQuestion() {
      try {
        await this.$refs.questionForm.validate()

        // 验证选择题必须有选项
        if (this.editingQuestion.type !== 'text' && this.editingQuestion.options.length === 0) {
          this.$message.warning('请至少添加一个选项')
          return
        }

        // 验证选项内容
        if (this.editingQuestion.type !== 'text') {
          const emptyOptions = this.editingQuestion.options.filter(opt => !opt.text || opt.text.trim() === '')
          if (emptyOptions.length > 0) {
            this.$message.warning('请完善所有选项内容')
            return
          }
        }

        this.saving = true

        // 构建保存数据
        const questionData = {
          title: this.editingQuestion.title,
          type: this.editingQuestion.type,
          scoreId: this.editingQuestion.scoreItem || '',
          maxScore: 100,
          required: 1,
          sortOrder: this.editingQuestion._index !== undefined ?
            this.questions[this.editingQuestion._index].sortOrder :
            this.questions.length + 1
        }

        // 处理选项数据
        if (this.editingQuestion.type !== 'text' && this.editingQuestion.options.length > 0) {
          questionData.options = this.editingQuestion.options.map((option, optionIndex) => ({
            id: option.id && typeof option.id === 'number' && option.id < Date.now() - 1000000 ? option.id : null,
            optionText: option.text || '',
            optionValue: option.text || '',
            score: option.score || 0,
            sortOrder: optionIndex + 1
          }))
        } else {
          questionData.options = []
        }

        let response
        if (this.editingQuestion.id && this.editingQuestion.id < Date.now() - 1000000) {
          // 更新现有题目
          questionData.id = this.editingQuestion.id
          response = await updateQuestionApi(this.questionnaireInfo.id, this.editingQuestion.id, questionData)
        } else {
          // 新增题目
          response = await saveQuestionApi(this.questionnaireInfo.id, questionData)
        }

        if (response) {
          // 更新本地数据
          const updatedQuestionData = {
            id: response.id || this.editingQuestion.id,
            type: questionData.type,
            title: questionData.title,
            scoreItem: questionData.scoreItem,
            optionCount: questionData.options.length,
            sortOrder: questionData.sortOrder,
            options: this.editingQuestion.options
          }

          if (this.editingQuestion._index !== undefined) {
            this.$set(this.questions, this.editingQuestion._index, updatedQuestionData)
            this.$message.success('题目更新成功')
          } else {
            this.questions.push(updatedQuestionData)
            this.$message.success('题目添加成功')
          }

          this.questionDialogVisible = false
        }
      } catch (error) {
        console.error('保存题目失败:', error)
        if (error.response && error.response.data && error.response.data.message) {
          this.$message.error(`保存失败：${error.response.data.message}`)
        } else {
          this.$message.error('保存失败，请重试')
        }
      } finally {
        this.saving = false
      }
    },

    // 重置题目表单
    resetQuestionForm() {
      this.editingQuestion = {
        id: null,
        type: 'single',
        title: '',
        scoreItem: '',
        optionCount: 0,
        options: []
      }
    },

    // 添加选项
    addOption() {
      const optionCount = this.editingQuestion.options.length
      this.editingQuestion.options.push({
        id: Date.now(),
        text: '',
        score: optionCount + 1
      })

      this.editingQuestion.optionCount = this.editingQuestion.options.length

      this.$nextTick(() => {
        const inputs = this.$el.querySelectorAll('.option-text-input input')
        if (inputs.length > 0) {
          inputs[inputs.length - 1].focus()
        }
      })
    },

    // 删除选项
    removeOption(index) {
      this.editingQuestion.options.splice(index, 1)
      this.editingQuestion.optionCount = this.editingQuestion.options.length
    },

    // 获取题目类型标签类型
    getQuestionTypeTagType(type) {
      const typeMap = {
        single: 'primary',
        multi: 'success',
        text: 'info'
      }
      return typeMap[type] || 'info'
    },

    // 获取题目类型文本
    getQuestionTypeText(type) {
      const typeMap = {
        single: '单选题',
        multi: '多选题',
        text: '简答题'
      }
      return typeMap[type] || '未知'
    },

    // 获取选项标签（A、B、C...）
    getOptionLabel(index) {
      return String.fromCharCode(65 + index)
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        0: 'danger',
        1: 'success',
        2: 'warning'
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '禁用',
        1: '启用',
        2: '草稿'
      }
      return statusMap[status] || '未知'
    },

    // 获取企业类型文本
    getEnterpriseTypeText(typeCode) {
      const typeMap = {
        'A': 'A类',
        'B': 'B类',
        'C': 'C类',
        'D': 'D类',
        'E': 'E类'
      }
      return typeMap[typeCode] || typeCode
    },

    // 获取评分项名称
    getScoreItemName(id) {
      if (!id) return '暂无'
      const option = this.scoreItemOptions.find(option => option.value === id)
      if (option) {
        return option.label
      }

      return `评分项${id}`
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/shared-styles.less";

.questionnaire-info {
  margin-bottom: 24px;
}

.enterprise-type-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.empty-text {
  color: #909399;
  font-style: italic;
}

// 关联评分项显示样式
.score-item-display {
  .empty-text {
    color: #909399;
    font-style: italic;
    font-size: 12px;
  }
}

.questions-config-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sort-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.sort-number {
  font-weight: bold;
  color: #409EFF;
  font-size: 16px;
  min-width: 20px;
  text-align: center;
}

.sort-controls {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.options-section {
  margin-top: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.section-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
}

.header-subtitle {
  font-size: 12px;
  color: #909399;
  margin: 0;
}

.options-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #e9ecef;
  border-radius: 6px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.header-item {
  display: flex;
  align-items: center;

  &.index-header {
    width: 60px;
    justify-content: center;
  }

  &.content-header {
    flex: 1;
  }

  &.score-header {
    width: 120px;
    justify-content: center;
  }

  &.action-header {
    width: 60px;
    justify-content: center;
  }
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-item {
  background: white;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;

  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  }
}

.option-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.option-index {
  width: 60px;
  display: flex;
  justify-content: center;
}

.index-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  font-size: 14px;
  font-weight: 600;
}

.option-text-input {
  flex: 1;
}

.option-score-input {
  width: 120px;
}

.option-actions {
  width: 60px;
  display: flex;
  justify-content: center;
}

.empty-options {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
  font-size: 14px;

  i {
    font-size: 48px;
    margin-bottom: 12px;
    color: #c0c4cc;
  }

  span {
    text-align: center;
    line-height: 1.5;
  }
}

// 题目编辑弹窗样式优化
/deep/ .question-edit-dialog {
  border-radius: 12px;
  overflow: hidden;

  .el-dialog__header {
    padding: 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 0;
    max-height: 70vh;
    overflow-y: auto;
  }

  .el-dialog__footer {
    padding: 0;
    border-top: 1px solid #f0f0f0;
  }

  // 关联评分项选择框宽度调整
  .el-form .el-row:nth-child(1) .el-col:nth-child(2) {
    .el-select,
    .el-input,
    .el-input__inner {
      width: 270px !important;
    }
  }
}

// 弹窗头部样式
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
  color: white;

  .header-left {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .header-title-row {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .header-icon {
    font-size: 24px;
    color: white;
    opacity: 0.9;
  }

  .dialog-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.2;
    color: white;
  }

  .dialog-subtitle {
    margin: 0;
    font-size: 14px;
    opacity: 0.8;
    line-height: 1.2;
    color: white;
    margin-left: 36px; // 与标题对齐（图标宽度24px + 间距12px）
  }

  .close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      color: white;
    }
  }
}

// 弹窗内容区域
.dialog-content {
  padding: 24px;
}

// 基本信息区域
.basic-info-section {
  margin-bottom: 24px;
}

// 区域标题样式
.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;

  i {
    font-size: 18px;
    color: #D7A256;
  }

  .title-badge {
    margin-left: 8px;

    span {
      display: inline-block;
      padding: 2px 8px;
      background: rgba(215, 162, 86, 0.1);
      color: #D7A256;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
  }
}

// 带操作按钮的标题
.section-title-with-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .add-option-btn {
    background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
    border: none;
    border-radius: 6px;
    font-weight: 500;

    &:hover {
      opacity: 0.9;
      transform: translateY(-1px);
    }
  }
}

// 弹窗底部样式
.dialog-footer {
  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #fafbfc;
  }

  .footer-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #606266;
    font-size: 14px;

    i {
      color: #909399;
    }
  }

  .footer-actions {
    display: flex;
    gap: 12px;

    .cancel-btn {
      color: #606266;
      border-color: #dcdfe6;

      &:hover {
        color: #D7A256;
        border-color: #D7A256;
      }
    }

    .save-btn {
      background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
      border: none;
      font-weight: 500;

      &:hover {
        opacity: 0.9;
        transform: translateY(-1px);
      }
    }
  }
}

// 选项配置区域优化
.options-section {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-top: 0;

  .options-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    font-weight: 600;
    color: #495057;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .option-item {
    background: #fafbfc;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #D7A256;
      box-shadow: 0 2px 12px rgba(215, 162, 86, 0.15);
      transform: translateY(-1px);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .index-badge {
    background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
    box-shadow: 0 2px 4px rgba(215, 162, 86, 0.3);
    font-weight: 600;

    &:hover {
      transform: scale(1.05);
    }
  }

  .empty-options {
    text-align: center;
    padding: 48px 20px;
    color: #909399;
    background: #fafbfc;
    border: 2px dashed #e4e7ed;
    border-radius: 8px;

    i {
      font-size: 56px;
      margin-bottom: 16px;
      color: #c0c4cc;
      display: block;
    }

    span {
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

// 输入框和按钮优化
/deep/ .question-edit-dialog {
  .option-text-input {
    .el-input__inner {
      border-radius: 6px;
      border-color: #e4e7ed;

      &:focus {
        border-color: #D7A256;
        box-shadow: 0 0 0 2px rgba(215, 162, 86, 0.1);
      }
    }
  }

  .option-score-input {
    .el-input-number__decrease,
    .el-input-number__increase {
      border-radius: 4px;
    }

    .el-input__inner {
      border-radius: 6px;
      text-align: center;
    }
  }

  .option-actions {
    .el-button {
      &.is-circle {
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  /deep/ .question-edit-dialog {
    width: 95% !important;
    margin: 0 auto;
  }

  .dialog-header {
    padding: 16px 20px;

    .header-icon {
      width: 40px;
      height: 40px;
      font-size: 20px;
    }

    .dialog-title {
      font-size: 18px;
    }
  }

  .dialog-content {
    padding: 20px;
  }

  .options-section {
    padding: 16px;
  }

  .option-content {
    flex-direction: column;
    gap: 12px;

    .option-index,
    .option-text-input,
    .option-score-input,
    .option-actions {
      width: 100%;
    }

    .option-actions {
      display: flex;
      justify-content: center;
    }
  }
}

// 关联评分项选择框样式 - 单选模式
/deep/ .el-form-item .el-select {
  width: 240px !important;
  min-width: 240px !important;
  max-width: 300px !important;
  box-sizing: border-box;

  // 输入框样式
  .el-input {
    width: 100% !important;

    .el-input__inner {
      width: 100% !important;
      padding-right: 30px; // 为下拉箭头留出空间
    }
  }
}
</style>
