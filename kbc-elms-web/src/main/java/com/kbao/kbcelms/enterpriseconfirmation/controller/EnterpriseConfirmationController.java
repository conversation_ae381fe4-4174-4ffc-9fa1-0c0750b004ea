package com.kbao.kbcelms.enterpriseconfirmation.controller;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.web.BaseController;
import com.kbao.kbcelms.enterpriseconfirmation.service.EnterpriseConfirmationService;
import com.kbao.kbcelms.enterpriseconfirmation.bean.EnterpriseConfirmationSearchVo;
import com.kbao.kbcelms.enterpriseconfirmation.bean.EnterpriseConfirmationListVo;
import com.kbao.kbcelms.enterpriseconfirmation.bean.EnterpriseConfirmationDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description 企业确权管理Controller
 * @Date 2025-08-20
 */
@RestController
@RequestMapping("/api/enterprise/confirmation")
@Api(tags = "企业确权管理")
public class EnterpriseConfirmationController extends BaseController {

    @Autowired
    private EnterpriseConfirmationService enterpriseConfirmationService;

    @PostMapping("/page")
    @ApiOperation("分页查询企业确权列表")
    public Result<PageInfo<EnterpriseConfirmationListVo>> page(@RequestBody PageRequest<EnterpriseConfirmationSearchVo> pageRequest) {
        PageInfo<EnterpriseConfirmationListVo> result = enterpriseConfirmationService.getConfirmationList(pageRequest);
        return Result.success(result);
    }

    @GetMapping("/detail/{id}")
    @ApiOperation("获取企业确权详情")
    public Result<EnterpriseConfirmationDetailVo> detail(@PathVariable Integer id) {
        EnterpriseConfirmationDetailVo result = enterpriseConfirmationService.getConfirmationDetail(id);
        return Result.success(result);
    }
}
