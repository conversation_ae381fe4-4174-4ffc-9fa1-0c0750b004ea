<template>
  <div class="project-summary">
    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool" class="summary-tool"></TableToolTemp>
    <!-- <SearchForm :searchForm="initParam" :searchFormTemp="searchFormTemp" @normalSearch="normalSearch"
      @normalResetQuery="normalResetQuery"></SearchForm> -->

    <!-- 项目总结表格 -->
    <el-table :data="tableData" class="dt-table" style="width: 100%" v-hover row-key="id">
      <el-table-column align="center" prop="fileName" label="总结名称" >
        <template slot-scope="scope">
          <span class="summary-name">{{ scope.row.fileName }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="createId" label="上传人" >
        <template slot-scope="scope">
          <span>{{ scope.row.createId | getNickName(scope.row.createId) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="createTime" label="上传时间" >
        <template slot-scope="scope">
          <div>
            {{ scope.row.createTime }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <div class="action-buttons">
            <el-button class="btn-center" type="text" v-if="hasPermission('elms:opportunity:summary:download')" @click="downloadSummary(scope.row)">
              下载
            </el-button>
            <el-button class="btn-center" type="text" v-if="hasPermission('elms:opportunity:summary:delete')" @click="deleteSummary(scope.row)">
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam"
      :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>

    <!-- 上传项目总结弹窗 -->
    <DtPopup :isShow.sync="showUploadPopup" @close="closeUploadPopup" title="上传项目总结" center :footer="false" width="600px">
      <div class="upload-form">
        <el-form ref="uploadFormRef" :model="uploadForm" :rules="uploadRules" label-width="120px">
          <el-form-item label="项目总结文件" class="dt-input-big" prop="file" required>
            <div class="file-display" v-if="uploadForm.file">
              <span class="file-name">{{ uploadForm.file.name }}</span>
              <el-button type="text" class="delete-btn" @click="deleteFile">
                删除
              </el-button>
            </div>
            <div class="upload-area" v-else @click="triggerFileUpload">
              <i class="el-icon-upload"></i>
              <span>上传</span>
            </div>
            <div class="file-tips">
              支持ppt、pdf、word、excel、jpg、png文件,单个文件不大于20M
            </div>
          </el-form-item>
        </el-form>
        <div class="form-actions">
          <el-button @click="closeUploadPopup">取消</el-button>
          <el-button type="primary" @click="submitUploadForm" :loading="submitLoading" :disabled="!uploadForm.file">
            保存
          </el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 删除确认弹窗 -->
    <DtPopup :isShow.sync="showDeletePopup" @close="closeDeletePopup" title="确认删除" center :footer="false" width="500px">
      <div class="delete-confirm">
        <div class="confirm-content">
          <p>删除后无法恢复，请确认是否删除该项目总结？</p>
        </div>
        <div class="confirm-actions">
          <el-button @click="closeDeletePopup">取消</el-button>
          <el-button :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color }" @click="confirmDelete" :loading="deleteLoading">删除</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 隐藏的文件上传输入框 -->
    <input
      ref="fileInput"
      type="file"
      accept=".ppt,.pptx,.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
      style="display: none"
      @change="onFileChange"
    />
  </div>
</template>

<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import Pagination from "@/components/layouts/Pagination";
import { getOpportunitySummaryList, addOpportunitySummary,uploadFile, deleteOpportunitySummary } from "@/api/workbench";

export default {
  name: "projectSummary",
  inject: ['opportunityDetailFn', 'opportunityFn', 'enterpriseFn', 'opportunityProcessLogFn'],
  data() {
    return {
      toolListProps: {
        toolTitle: "项目总结管理",
        toolList: [
          {
            name: "上传项目总结",
            // icon: "el-icon-upload",
            btnCode: "elms:opportunity:summary:upload"
          }
        ]
      },
      // 表格数据
      tableData: [
        
      ],
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          opportunityId: this.$route.query.id
        }
      },
      // searchFormTemp: [
      //   {
      //     label: "总结名称",
      //     name: "summaryName",
      //     type: "input",
      //     placeholder: "请输入总结名称"
      //   },
      //   {
      //     label: "上传人",
      //     name: "uploader",
      //     type: "input",
      //     placeholder: "请输入上传人"
      //   }
      // ],
      total: 0,

      // 上传表单相关
      showUploadPopup: false,
      uploadForm: {
        file: null,
        fileName: "",
        filePath: "",
        fileType: ""
      },
      uploadRules: {
        file: [
          { required: true, message: "请选择要上传的文件", trigger: "change" }
        ]
      },
      submitLoading: false,

      // 删除相关
      showDeletePopup: false,
      deleteItem: {},
      deleteLoading: false
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup,
    Pagination
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    // 从父组件获取注入的数据
    injectedOpportunityDetail() {
      return this.opportunityDetailFn ? this.opportunityDetailFn() : null;
    },
    injectedOpportunity() {
      return this.opportunityFn ? this.opportunityFn() : null;
    },
    injectedEnterprise() {
      return this.enterpriseFn ? this.enterpriseFn() : null;
    },
    injectedOpportunityProcessLog() {
      return this.opportunityProcessLogFn ? this.opportunityProcessLogFn() : null;
    },
    // 获取企客用户权限信息
    qikeUserInfo() {
      return this.$store.getters["layoutStore/getQikeUserInfo"];
    },
  },
  async created() {
    this.initData();
  },
  methods: {
    // 检查用户是否有指定权限
    hasPermission() {
      return (permissionCode) => {
        if (!this.qikeUserInfo || !this.qikeUserInfo.roleAuthStr) {
          return false;
        }
        const roleAuthStr = this.qikeUserInfo.roleAuthStr;
        const permissions = roleAuthStr.split(',').map(item => item.trim());
        return permissions.includes(permissionCode);
      };
    },
    async initData() {
      let res = await getOpportunitySummaryList(this.initParam);
      if(res) {
        this.tableData = res.list;
        this.total = res.total;
      }
    },

    handleTool(item) {
      if (item.name === "上传项目总结") {
        this.uploadSummary();
      }
    },

    // 上传项目总结
    uploadSummary() {
      this.uploadForm = {
        file: null,
        fileName: "",
        filePath: "",
        fileType: ""
      };
      this.showUploadPopup = true;
    },

    // 触发文件上传
    triggerFileUpload() {
      this.$refs.fileInput.click();
    },

    // 文件选择变化
    async onFileChange(event) {
      const file = event.target.files[0];
      if (!file) return;

      // 检查文件类型
      const allowedTypes = [
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'image/jpeg',
        'image/png'
      ];

      if (!allowedTypes.includes(file.type)) {
        this.$message.error('不支持的文件类型，请选择ppt、pdf、word、excel、jpg、png文件');
        return;
      }

      // 检查文件大小 (20MB = 20 * 1024 * 1024 bytes)
      const maxSize = 20 * 1024 * 1024;
      if (file.size > maxSize) {
        this.$message.error('文件大小不能超过20M');
        return;
      }

      try {
        // 显示上传进度
        this.$message.info('文件上传中...');
        const uploadRes = await uploadFile({fileType:"summary",file:file});
        
        if (uploadRes) {
          // 将返回的数据构造成 uploadForm 格式
          this.uploadForm = {
            file: file,
            fileName: file.name,
            filePath:  uploadRes,
            fileType: this.getFileType(file.name)
          };
          
          this.$message.success('文件上传成功');
        } else {
          this.$message.error( '文件上传失败');
        }
      } catch (error) {
        this.$message.error('文件上传失败，请重试');
      }
      
      event.target.value = '';
    },


    // 根据文件名获取文件类型
    getFileType(fileName) {
      const extension = fileName.split('.').pop().toLowerCase();
      const typeMap = {
        'ppt': 'ppt',
        'pptx': 'ppt',
        'pdf': 'pdf',
        'doc': 'doc',
        'docx': 'doc',
        'xls': 'xls',
        'xlsx': 'xls',
        'jpg': 'jpg',
        'jpeg': 'jpg',
        'png': 'png'
      };
      return typeMap[extension] || 'unknown';
    },

    // 删除文件
    deleteFile() {
      this.uploadForm.file = null;
    },

    // 下载项目总结
    downloadSummary(row) {
      // 创建一个隐藏的 iframe 来下载文件，避免页面跳转
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.src = row.filePath;
      document.body.appendChild(iframe);
      
      // 设置下载属性
      const link = document.createElement('a');
      link.href = row.filePath;
      link.download = row.fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // 延迟移除 iframe
      setTimeout(() => {
        document.body.removeChild(iframe);
      }, 1000);
      
      this.$message.success("文件下载成功");
    },

    // 删除项目总结
    deleteSummary(row) {
      this.deleteItem = { ...row };
      this.showDeletePopup = true;
    },

    // 提交上传表单
    async submitUploadForm() {
      try {
        await this.$refs.uploadFormRef.validate();
        this.submitLoading = true;

        // 使用已经上传的文件信息
        let uploadForm = {
          opportunityId: this.$route.query.id,
          fileName: this.uploadForm.fileName, // 文件名称
          filePath: this.uploadForm.filePath, // 文件路径URL
          fileType: this.uploadForm.fileType // doc/pdf/jpg
        }

        let res = await addOpportunitySummary(uploadForm);
        if(res) {
          this.$message.success("项目总结上传成功");
          this.closeUploadPopup();
          this.initData();
        }
      } catch (error) {
        console.error("表单验证失败:", error);
      } finally {
        this.submitLoading = false;
      }
    },

    // 确认删除
    async confirmDelete() {
      try {
        this.deleteLoading = true;

        let res = await deleteOpportunitySummary({id: this.deleteItem.id});
        if(res) {
          this.$message.success("项目总结删除成功");
          this.closeDeletePopup();
          this.initData();
        }
      } catch (error) {
        this.$message.error("删除失败");
      } finally {
        this.deleteLoading = false;
      }
    },

    // 关闭上传弹窗
    closeUploadPopup() {
      this.showUploadPopup = false;
      this.uploadForm.file = null;
      this.$nextTick(() => {
        if (this.$refs.uploadFormRef) {
          this.$refs.uploadFormRef.resetFields();
        }
      });
    },

    // 关闭删除弹窗
    closeDeletePopup() {
      this.showDeletePopup = false;
      this.deleteItem = {};
    },

    // 搜索
    normalSearch(data) {
      console.log("搜索参数:", data);
      this.initData();
    },

    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.initData();
    },

    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initData();
    },

    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.initData();
    }
  }
};
</script>

<style lang="less" scoped>
.project-summary {
  .summary-name {
    font-weight: 500;
    color: #303133;
  }

  .time-cell {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #909399;
    font-size: 13px;
    text-align: center;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;

    .el-button {
      padding: 4px 8px;
      font-size: 12px;

      i {
        margin-right: 2px;
      }
    }
  }

  .upload-form {
    padding: 20px;

    .file-display {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 12px;
      background: #f5f7fa;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      margin-bottom: 8px;

      .file-name {
        color: #606266;
        font-size: 14px;
      }

      .delete-btn {
        padding: 0;
        font-size: 12px;
        i {
          margin-right: 4px;
        }
      }
    }

    .upload-area {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      background: #fafafa;
      cursor: pointer;
      transition: border-color 0.3s;
      margin-bottom: 8px;

      i {
        font-size: 24px;
        color: #8c939d;
        margin-bottom: 8px;
      }

      span {
        color: #8c939d;
        font-size: 14px;
      }
    }

    .file-tips {
      color: #909399;
      font-size: 12px;
      line-height: 1.4;
    }

    .form-actions {
      margin-top: 20px;
      padding-top: 20px;
      text-align: center;
      border-top: 1px solid #e4e7ed;
    }
  }

  .delete-confirm {
    padding: 0 20px 20px 20px;
    text-align: center;

    .confirm-content {
      margin-bottom: 20px;

      p {
        margin: 8px 0;
        color: #606266;
        font-size: 14px;
      }
    }

    .confirm-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }
}
</style> 