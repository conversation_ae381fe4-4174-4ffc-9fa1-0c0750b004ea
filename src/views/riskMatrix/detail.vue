<template>
  <div class="risk-matrix-detail-container" v-loading="loading" element-loading-text="加载报告数据中...">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <span class="page-title"><i class="el-icon-s-data"></i> 风险矩阵分析报告</span>
          <p class="page-subtitle">
            {{ reportData.enterpriseName ? `${reportData.enterpriseName} - ` : '' }}详细展示风险评估计算过程和结果分析
          </p>
        </div>
        <div class="action-section">
          <el-button @click="handleBack" class="back-btn">返回</el-button>
          <el-button type="primary" icon="el-icon-download" @click="exportReport" class="export-btn">导出报告</el-button>
        </div>
      </div>
    </div>

    <!-- 基本信息 -->
    <div class="basic-info-section">
      <div class="section-header">
        <span class="section-title">基本信息</span>
      </div>
      <div class="info-cards">
        <div class="info-card">
          <div class="card-icon">
            <i class="el-icon-office-building"></i>
          </div>
          <div class="card-content">
            <div class="card-label">企业类型</div>
            <div class="card-value">
              <el-tag :type="getTypeTagType(reportData.enterpriseType)" size="medium">
                {{ getTypeName(reportData.enterpriseType) }}
              </el-tag>
            </div>
          </div>
        </div>

        <div class="info-card">
          <div class="card-icon">
            <i class="el-icon-s-data"></i>
          </div>
          <div class="card-content">
            <div class="card-label">总分</div>
            <div class="card-value">{{ formatScore(reportData.totalScore) }}</div>
          </div>
        </div>

        <div class="info-card">
          <div class="card-icon">
            <i class="el-icon-s-marketing"></i>
          </div>
          <div class="card-content">
            <div class="card-label">平均分</div>
            <div class="card-value">{{ formatScore(reportData.averageScore) }}</div>
          </div>
        </div>

        <div class="info-card">
          <div class="card-icon">
            <i class="el-icon-warning"></i>
          </div>
          <div class="card-content">
            <div class="card-label">风险等级</div>
            <div class="card-value">
              <el-tag :type="getRiskLevelTagType(reportData.overallRiskLevel)" size="medium">
                {{ reportData.overallRiskLevel }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 雷达图 -->
    <div class="radar-chart-section">
      <div class="section-header">
        <span class="section-title">风险矩阵雷达图</span>
        <div class="chart-controls" v-if="getRadarChartData().length > 1">
          <el-radio-group v-model="selectedRadarIndex" @change="switchRadarChart">
            <el-radio-button
              v-for="(chart, index) in getRadarChartData()"
              :key="index"
              :label="index">
              {{ chart.name || `矩阵${index + 1}` }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="chart-container">
        <div ref="radarChart" class="radar-chart"></div>
        <div v-if="getRadarChartData().length === 0" class="no-data-tip">
          <i class="el-icon-warning"></i>
          <span>暂无雷达图数据</span>
        </div>
      </div>
    </div>

    <!-- 计算过程详情 -->
    <div class="calculation-details-section">
      <div class="section-header">
        <span class="section-title">计算过程详情</span>
        <p class="section-subtitle">详细展示从问卷答案到最终风险等级的完整计算过程</p>
      </div>

      <!-- 问卷答案 -->
      <div class="detail-section">
        <div class="detail-title">
          <i class="el-icon-edit"></i>
          问卷答案分析
        </div>
        <div class="detail-content">
          <el-table :data="getQuestionnaireAnswers()" stripe class="detail-table">
            <el-table-column prop="questionTitle" label="问题" min-width="300" align="center" />
            <el-table-column prop="optionValue" label="答案内容" width="400" align="center" />
<!--            <el-table-column prop="optionValue" label="选项值" width="100" align="center" />-->
            <el-table-column prop="score" label="得分" width="100" align="center">
              <template slot-scope="scope">
                <span class="score-value">{{ scope.row.score || scope.row.optionScore || 0 }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 评分项计算 -->
      <div class="detail-section">
        <div class="detail-title">
          <i class="el-icon-s-flag"></i>
          评分项计算过程
        </div>
        <div class="detail-content">
          <el-table :data="getScoreItemResults()" stripe class="detail-table">
            <el-table-column prop="scoreItemName" label="评分项" width="200" align="center" />
            <el-table-column prop="formulaName" label="关联公式" width="200" align="center" />
            <el-table-column prop="questionnaireScore" label="问卷得分" width="100" align="center" />
            <el-table-column prop="calculatedScore" label="公式计算得分" width="120" align="center" />
            <el-table-column prop="coefficient" label="系数" width="80" align="center" />
            <el-table-column prop="finalScore" label="最终得分" width="100" align="center">
              <template slot-scope="scope">
                <span class="final-score">{{ formatScore(scope.row.finalScore) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="计算详情" min-width="200" align="center">
              <template slot-scope="scope">
                <el-button type="text" @click="showCalculationDetails(scope.row)" class="detail-btn">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 核心类别计算 -->
      <div class="detail-section">
        <div class="detail-title">
          <i class="el-icon-s-grid"></i>
          核心类别计算结果
        </div>
        <div class="detail-content">
          <el-table :data="reportData.categoryResults" stripe class="detail-table">
            <el-table-column prop="categoryName" label="类别名称" width="200" align="center" />
            <el-table-column prop="calculationMethod" label="计算方式" width="120" align="center">
              <template slot-scope="scope">
                <el-tag :type="scope.row.calculationMethod === 'sum' ? 'success' : 'warning'" size="small">
                  {{ scope.row.calculationMethod === 'sum' ? '加和' : '平均数' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="categoryScore" label="类别得分" width="100" align="center">
              <template slot-scope="scope">
                <span class="category-score">{{ formatScore(scope.row.categoryScore) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="档次" width="120" align="center">
              <template slot-scope="scope">
                <el-tag :type="getLevelTagType(scope.row.level.name)" size="small">
                  {{ scope.row.level.name }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="level.description" label="档次描述" min-width="300" align="center" />
            <el-table-column label="包含评分项" width="150" align="center">
              <template slot-scope="scope">
                <el-button type="text" @click="showCategoryDetails(scope.row)" class="detail-btn">
                  查看详情 ({{ scope.row.scoreItems ? scope.row.scoreItems.length : 0 }}个)
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 建议措施 -->
    <div class="suggestions-section">
      <div class="section-header">
        <span class="section-title">风险建议措施</span>
      </div>
      <div class="suggestions-content">
        <div v-for="suggestion in getSuggestions()" :key="suggestion.sort || suggestion.suggestionType" class="suggestion-card">
          <div class="suggestion-header">
            <i :class="suggestion.suggestionType === 'OVERALL' ? 'el-icon-lightbulb' : 'el-icon-warning'"></i>
            <span>{{ suggestion.title }}</span>
            <el-tag v-if="suggestion.priority" :type="getPriorityTagType(suggestion.priority)" size="mini">
              {{ getPriorityText(suggestion.priority) }}
            </el-tag>
          </div>
          <div class="suggestion-content">
            {{ suggestion.content }}
          </div>
        </div>
      </div>
    </div>

    <!-- 计算详情对话框 -->
    <el-dialog title="评分项计算详情" :visible.sync="calculationDetailVisible" width="800px">
      <div v-if="selectedCalculationDetail">
        <div class="detail-item">
          <label>评分项名称：</label>
          <span>{{ selectedCalculationDetail.scoreItemName }}</span>
        </div>
        <div class="detail-item">
          <label>关联公式：</label>
          <span>{{ selectedCalculationDetail.formulaName }}</span>
        </div>
        <div class="detail-item">
          <label>计算过程说明：</label>
          <span>{{ selectedCalculationDetail.calculationDescription || '暂无详细说明' }}</span>
        </div>
        <div class="detail-item">
          <label>问卷得分：</label>
          <span>{{ formatScore(selectedCalculationDetail.questionnaireScore) }}</span>
        </div>
        <div class="detail-item">
          <label>公式计算得分：</label>
          <span>{{ formatScore(selectedCalculationDetail.calculatedScore) }}</span>
        </div>
        <div class="detail-item">
          <label>系数：</label>
          <span>{{ selectedCalculationDetail.coefficient || 1 }}</span>
        </div>
        <div class="detail-item">
          <label>最终得分：</label>
          <span class="result-value">{{ formatScore(selectedCalculationDetail.finalScore) }}</span>
        </div>
      </div>
    </el-dialog>

    <!-- 类别详情对话框 -->
    <el-dialog title="类别详情" :visible.sync="categoryDetailVisible" width="800px">
      <div v-if="selectedCategory">
        <div class="detail-item">
          <label>类别名称：</label>
          <span>{{ selectedCategory.categoryName }}</span>
        </div>
        <div class="detail-item">
          <label>计算方式：</label>
          <span>{{ selectedCategory.calculationMethod === 'sum' ? '加和' : '平均数' }}</span>
        </div>
        <div class="detail-item">
          <label>类别得分：</label>
          <span class="score-value">{{ formatScore(selectedCategory.categoryScore) }}</span>
        </div>
        <div class="detail-item" v-if="selectedCategory.level">
          <label>档次信息：</label>
          <div>
            <el-tag :type="getLevelTagType(selectedCategory.level.name)" size="small">
              {{ selectedCategory.level.name }}
            </el-tag>
            <span style="margin-left: 8px;">{{ selectedCategory.level.description }}</span>
          </div>
        </div>
        <div class="detail-item" v-if="selectedCategory.scoreItems && selectedCategory.scoreItems.length > 0">
          <label>评分项列表：</label>
          <div class="score-items-list">
            <el-table :data="selectedCategory.scoreItems" stripe>
              <el-table-column prop="scoreItemName" label="评分项名称" min-width="200" />
              <el-table-column prop="finalScore" label="得分" width="100" align="center">
                <template slot-scope="scope">
                  {{ formatScore(scope.row.finalScore) }}
                </template>
              </el-table-column>
              <el-table-column prop="weight" label="权重" width="100" align="center" />
              <el-table-column label="加权得分" width="100" align="center">
                <template slot-scope="scope">
                  {{ formatScore((scope.row.finalScore || 0) * (scope.row.weight || 1)) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getLatestReport, getReportById } from '@/api/riskMatrix'

export default {
  name: 'RiskMatrixDetail',
  data() {
    return {
      reportData: {
        id: '',
        enterpriseId: null,
        enterpriseName: '',
        enterpriseType: '',
        totalScore: 0,
        averageScore: 0,
        overallRiskLevel: '中风险',
        categoryResults: [],
        calculationProcess: {
          questionnaireAnswers: [],
          scoreItemResults: [],
          basicParameters: {},
          riskMatrix: null
        },
        suggestions: [],
        radarChartDatas: [],
        createTime: new Date().toLocaleString()
      },
      loading: false,
      calculationDetailVisible: false,
      categoryDetailVisible: false,
      selectedCalculationDetail: null,
      selectedCategory: null,
      radarChartInstance: null,
      selectedRadarIndex: 0
    }
  },
  mounted() {
    this.loadReportData()
    this.$nextTick(() => {
      this.initRadarChart()
    })
  },
  beforeDestroy() {
    // 清理图表实例
    if (this.radarChartInstance) {
      this.radarChartInstance.dispose()
      this.radarChartInstance = null
    }
    // 移除resize监听器
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    async loadReportData() {
      try {
        this.loading = true

        // 从路由参数获取企业ID或报告ID
        const { enterpriseId, reportId } = this.$route.params

        let reportData = null

        if (reportId) {
          // 根据报告ID获取报告详情
          const response = await getReportById(reportId)
          if (response.code === 200) {
            reportData = response.data
          }
        } else if (enterpriseId) {
          // 根据企业ID获取最新报告
          const response = await getLatestReport(enterpriseId)
          if (response.code === 200) {
            reportData = response.data
          }
        }

        if (reportData) {
          this.reportData = {
            ...this.reportData,
            ...reportData,
            // 确保数组字段不为空
            categoryResults: reportData.categoryResults || [],
            suggestions: reportData.suggestions || [],
            radarChartDatas: reportData.radarChartDatas || [],
            calculationProcess: {
              questionnaireAnswers: reportData.calculationProcess?.questionnaireAnswers || [],
              scoreItemResults: reportData.calculationProcess?.scoreItemResults || [],
              basicParameters: reportData.calculationProcess?.basicParameters || {},
              riskMatrix: reportData.calculationProcess?.riskMatrix || null
            }
          }

          // 数据加载完成后重新初始化雷达图
          this.$nextTick(() => {
            this.initRadarChart()
          })
        } else {
          this.$message.warning('未找到相关报告数据')
        }

      } catch (error) {
        console.error('加载报告数据失败:', error)
        this.$message.error('加载报告数据失败')
      } finally {
        this.loading = false
      }
    },

    initRadarChart() {
      console.log('开始初始化雷达图...')

      const radarChartData = this.getRadarChartData()
      console.log('雷达图数据:', radarChartData)

      if (!radarChartData || radarChartData.length === 0) {
        console.log('没有可用的雷达图数据，无法绘制雷达图')
        return
      }

      // 确保选中的索引有效
      if (this.selectedRadarIndex >= radarChartData.length) {
        this.selectedRadarIndex = 0
      }

      const currentRadarData = radarChartData[this.selectedRadarIndex]
      if (!currentRadarData || !currentRadarData.riskMatrixLevels || currentRadarData.riskMatrixLevels.length === 0) {
        console.log('当前选中的雷达图数据无效')
        return
      }

      const chartDom = this.$refs.radarChart
      if (!chartDom) {
        console.error('雷达图DOM元素未找到')
        return
      }

      // 销毁已存在的图表实例
      if (this.radarChartInstance) {
        this.radarChartInstance.dispose()
      }

      const myChart = echarts.init(chartDom)
      this.radarChartInstance = myChart

      const option = {
        title: {
          text: currentRadarData.name || '风险矩阵雷达图',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            if (params.componentType === 'radar') {
              const data = params.data
              let result = `${data.name}<br/>`
              data.value.forEach((value, index) => {
                const indicator = currentRadarData.riskMatrixLevels[index]
                const categoryName = indicator.categoryName || indicator.description || `类别${indicator.categoryId}`
                result += `${categoryName}: ${value.toFixed(2)}分<br/>`
              })
              return result
            }
            return params.name + ': ' + params.value
          }
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: ['当前得分', '满分']
        },
        radar: {
          indicator: currentRadarData.riskMatrixLevels.map(level => ({
            name: level.categoryName || level.description || `类别${level.categoryId}`,
            max: 100,
            min: 0
          })),
          radius: '65%',
          splitNumber: 5,
          name: {
            textStyle: {
              fontSize: 12
            }
          }
        },
        series: [
          {
            name: '风险矩阵',
            type: 'radar',
            data: [
              {
                value: currentRadarData.riskMatrixLevels.map(level =>
                  parseFloat(level.score || 0)
                ),
                name: '当前得分',
                areaStyle: {
                  color: 'rgba(215, 162, 86, 0.3)'
                },
                lineStyle: {
                  color: '#D7A256',
                  width: 2
                },
                itemStyle: {
                  color: '#D7A256',
                  borderColor: '#D7A256',
                  borderWidth: 2
                },
                symbol: 'circle',
                symbolSize: 6
              },
              {
                value: currentRadarData.riskMatrixLevels.map(() => 100),
                name: '满分',
                lineStyle: {
                  color: '#E0E0E0',
                  type: 'dashed',
                  width: 1
                },
                itemStyle: {
                  color: '#E0E0E0'
                },
                symbol: 'none'
              }
            ]
          }
        ]
      }

      myChart.setOption(option)

      // 响应式调整
      this.handleResize = () => {
        if (this.radarChartInstance) {
          this.radarChartInstance.resize()
        }
      }
      window.addEventListener('resize', this.handleResize)

      console.log('雷达图初始化完成')
    },

    getTypeTagType(type) {
      const typeMap = {
        'A类': 'success',
        'B类': 'warning',
        'C类': 'info',
        'D类': 'danger',
        'E类': 'info',
        'CDE': 'success'
      }
      return typeMap[type] || 'info'
    },

    getTypeName(type) {
      if (type === 'CDE') return 'CDE类'
      return type
    },

    getRiskLevelTagType(level) {
      const levelMap = {
        '低风险': 'success',
        '中风险': 'warning',
        '高风险': 'danger',
        '极高风险': 'danger'
      }
      return levelMap[level] || 'info'
    },

    getLevelTagType(levelName) {
      const levelMap = {
        '一档': 'danger',
        '二档': 'warning',
        '三档': 'info',
        '四档': 'success',
        '五档': 'success'
      }
      return levelMap[levelName] || 'info'
    },

    getSuggestion(level) {
      const suggestionMap = {
        '低风险': '继续保持现有管理措施，定期监控风险指标',
        '中风险': '加强监控，完善管理制度，制定改进计划',
        '高风险': '立即采取措施，制定改进计划，加强风险管控',
        '极高风险': '紧急处理，全面整改，建立应急响应机制'
      }
      return suggestionMap[level] || '建议加强管理'
    },

    getHighRiskCategories() {
      return this.reportData.categoryResults.filter(cat =>
        cat.level && (cat.level.name === '一档' || cat.level.name === '二档')
      )
    },

    // 获取问卷答案数据
    getQuestionnaireAnswers() {
      return this.reportData.calculationProcess?.questionnaireAnswers || []
    },

    // 获取评分项计算结果
    getScoreItemResults() {
      return this.reportData.calculationProcess?.scoreItemResults || []
    },

    // 获取建议措施
    getSuggestions() {
      return this.reportData.suggestions || []
    },

    // 格式化分数显示
    formatScore(score) {
      if (score === null || score === undefined) {
        return '0.00'
      }
      return typeof score === 'number' ? score.toFixed(2) : parseFloat(score || 0).toFixed(2)
    },

    // 获取优先级标签类型
    getPriorityTagType(priority) {
      const priorityMap = {
        'HIGH': 'danger',
        'MEDIUM': 'warning',
        'LOW': 'info'
      }
      return priorityMap[priority] || 'info'
    },

    // 获取优先级文本
    getPriorityText(priority) {
      const priorityMap = {
        'HIGH': '高',
        'MEDIUM': '中',
        'LOW': '低'
      }
      return priorityMap[priority] || priority
    },

    showCalculationDetails(detail) {
      this.selectedCalculationDetail = detail
      this.calculationDetailVisible = true
    },

    showCategoryDetails(category) {
      this.selectedCategory = category;
      this.categoryDetailVisible = true;
    },

    handleBack() {
      this.$router.back()
    },

    exportReport() {
      // 实现导出报告功能
      this.$message.success('报告导出功能开发中...')
    },

    // 获取档次标签类型
    getLevelTagType(levelName) {
      const levelMap = {
        '一档': 'success',
        '二档': 'primary',
        '三档': 'warning',
        '四档': 'danger',
        '五档': 'info'
      }
      return levelMap[levelName] || 'info'
    },

    // 获取雷达图数据
    getRadarChartData() {
      // 优先使用radarChartDatas，这是标准的雷达图数据结构
      if (this.reportData.radarChartDatas && this.reportData.radarChartDatas.length > 0) {
        return this.reportData.radarChartDatas
      }

      // 如果没有radarChartDatas，尝试从categoryResults构建
      if (this.reportData.categoryResults && this.reportData.categoryResults.length > 0) {
        return [{
          riskMatrixId: 1,
          name: '风险矩阵',
          riskMatrixLevels: this.reportData.categoryResults.map(category => ({
            categoryId: category.categoryId,
            categoryName: category.categoryName,
            score: category.categoryScore,
            description: category.categoryDescription || category.categoryName,
            levelId: category.level?.levelId,
            levelDescription: category.level?.description,
            scoreRange: category.level?.scoreRange
          })),
          sort: 1
        }]
      }

      return []
    },

    // 切换雷达图
    switchRadarChart(index) {
      console.log('切换到雷达图:', index)
      this.selectedRadarIndex = index
      this.$nextTick(() => {
        this.initRadarChart()
      })
    },

    // 从radarChartDatas中提取类别数据（保留兼容性）
    extractCategoryDataFromRadarChartDatas() {
      const radarChartData = this.getRadarChartData()
      if (!radarChartData || radarChartData.length === 0) {
        return []
      }

      const categoryData = []

      radarChartData.forEach(radarData => {
        if (radarData.riskMatrixLevels && radarData.riskMatrixLevels.length > 0) {
          radarData.riskMatrixLevels.forEach(level => {
            categoryData.push({
              categoryId: level.categoryId,
              categoryName: level.categoryName || level.description || `类别${level.categoryId}`,
              categoryScore: level.score,
              name: radarData.name,
              score: level.score
            })
          })
        }
      })

      console.log('从radarChartDatas提取的类别数据:', categoryData)
      return categoryData
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/shared-styles.less";

.risk-matrix-detail-container {
  min-height: 100vh;
  background: #fbf6ee;

  .page-header {
    background: white;
    padding: 24px 32px;
    border-bottom: 1px solid #f7ecdd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          color: #2c3e50;
          display: flex;
          align-items: center;
          gap: 12px;

          i {
            color: #D7A256;
            font-size: 28px;
          }
        }

        .page-subtitle {
          margin: 0;
          color: #7f8c8d;
          font-size: 14px;
        }
      }

      .action-section {
        .back-btn {
          margin-right: 12px;
        }
      }
    }
  }

  .basic-info-section,
  .radar-chart-section,
  .calculation-details-section,
  .suggestions-section {
    margin: 32px auto;
    max-width: 1200px;

    .section-header {
      background: white;
      padding: 20px 24px;
      border-radius: 8px 8px 0 0;
      border-bottom: 1px solid #f0f0f0;

      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #D7A256;
        }
      }

      .section-subtitle {
        margin: 8px 0 0 0;
        color: #7f8c8d;
        font-size: 14px;
      }
    }
  }

  .basic-info-section {
    .info-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      padding: 24px;
      background: white;
      border-radius: 0 0 8px 8px;

      .info-card {
        display: flex;
        align-items: center;
        padding: 20px;
        background: #fafafa;
        border-radius: 8px;
        border: 1px solid #f0f0f0;

        .card-icon {
          margin-right: 16px;

          i {
            font-size: 24px;
            color: #D7A256;
          }
        }

        .card-content {
          .card-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 4px;
          }

          .card-value {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
          }
        }
      }
    }
  }

  .radar-chart-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0;

      .chart-controls {
        .el-radio-group {
          .el-radio-button {
            margin-right: 8px;

            &:last-child {
              margin-right: 0;
            }
          }

          .el-radio-button__inner {
            padding: 8px 16px;
            font-size: 12px;
            border-radius: 4px;
          }
        }
      }
    }

    .chart-container {
      background: white;
      padding: 24px;
      border-radius: 0 0 8px 8px;
      position: relative;

      .radar-chart {
        width: 100%;
        height: 500px;
      }

      .no-data-tip {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 300px;
        color: #909399;
        font-size: 14px;

        i {
          font-size: 48px;
          margin-bottom: 16px;
          color: #E6A23C;
        }

        span {
          font-size: 16px;
        }
      }
    }
  }

  .calculation-details-section {
    .detail-section {
      background: white;
      margin-bottom: 24px;
      border-radius: 8px;
      overflow: hidden;

      .detail-title {
        padding: 16px 24px;
        background: #f8f9fa;
        border-bottom: 1px solid #f0f0f0;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #D7A256;
        }
      }

      .detail-content {
        padding: 24px;

        .detail-table {
          .selected-option {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;

            .option-label {
              font-weight: 600;
              color: #D7A256;
            }

            .option-text {
              color: #2c3e50;
            }

            .option-score {
              color: #7f8c8d;
              font-size: 12px;
            }
          }

          .score-value {
            font-weight: 600;
            color: #D7A256;
          }

          .final-score {
            font-weight: 600;
            color: #27ae60;
          }

          .category-score {
            font-weight: 600;
            color: #D7A256;
          }

          .detail-btn {
            color: #D7A256;
          }
        }
      }
    }
  }

  .suggestions-section {
    .suggestions-content {
      background: white;
      padding: 24px;
      border-radius: 0 0 8px 8px;

      .suggestion-card {
        margin-bottom: 16px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #D7A256;

        .suggestion-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          font-weight: 600;
          color: #2c3e50;

          i {
            color: #D7A256;
          }
        }

        .suggestion-content {
          color: #7f8c8d;
          line-height: 1.6;
        }
      }
    }
  }

  .detail-item {
    margin-bottom: 16px;

    label {
      font-weight: 600;
      color: #2c3e50;
      margin-right: 8px;
    }

    .variables-list {
      margin-top: 8px;

      .variable-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;

        .variable-name {
          font-weight: 600;
          color: #D7A256;
          min-width: 60px;
        }

        .variable-value {
          color: #2c3e50;
        }
      }
    }

    .result-value {
      font-weight: 600;
      color: #27ae60;
      font-size: 16px;
    }

    .score-items-list {
      margin-top: 8px;

      .score-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 4px;
        margin-bottom: 4px;

        .item-name {
          color: #2c3e50;
        }

        .item-score {
          font-weight: 600;
          color: #D7A256;
        }
      }
    }
  }
}
</style>
