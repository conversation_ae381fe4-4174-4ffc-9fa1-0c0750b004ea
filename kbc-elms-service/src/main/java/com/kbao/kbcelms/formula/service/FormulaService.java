package com.kbao.kbcelms.formula.service;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.constant.entity.ConstantConfig;
import com.kbao.kbcelms.constant.service.ConstantConfigService;
import com.kbao.kbcelms.constant.vo.ConstantConfigVO;
import com.kbao.kbcelms.formula.dao.FormulaCalculationLogMapper;
import com.kbao.kbcelms.formula.dao.FormulaMapper;
import com.kbao.kbcelms.formula.dao.FormulaVariableMapper;
import com.kbao.kbcelms.formula.dto.FormulaCalculationDTO;
import com.kbao.kbcelms.formula.dto.FormulaDTO;
import com.kbao.kbcelms.formula.dto.FormulaQueryDTO;
import com.kbao.kbcelms.formula.dto.FormulaVariableDTO;
import com.kbao.kbcelms.formula.entity.Formula;
import com.kbao.kbcelms.formula.entity.FormulaVariable;

import com.kbao.kbcelms.formula.enums.FormulaCategoryEnum;
import com.kbao.kbcelms.formula.vo.FormulaCalculationResultVO;
import com.kbao.kbcelms.formula.vo.FormulaVO;
import com.kbao.kbcelms.formula.vo.FormulaVariableVO;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import org.springframework.util.StringUtils;

import java.util.List;

import static com.github.pagehelper.page.PageMethod.startPage;

/**
 * 公式Service
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
public class FormulaService extends BaseSQLServiceImpl<Formula, Long, FormulaMapper> {

    @Autowired
    private FormulaVariableMapper formulaVariableMapper;
    @Autowired
    private FormulaCalculationService formulaCalculationService;
    @Autowired
    private ConstantConfigService constantConfigService;

    /**
     * 分页查询公式列表
     *
     * @param pageRequest 分页请求
     * @return 分页结果
     */
    public PageInfo<FormulaVO> getFormulaList(PageRequest<FormulaQueryDTO> pageRequest) {
        log.info("分页查询公式列表 - pageRequest: {}", pageRequest);

        FormulaQueryDTO param = pageRequest.getParam();
        if (param == null) {
            param = new FormulaQueryDTO();
        }

        // 设置分页
        startPage(pageRequest.getPageNum(), pageRequest.getPageSize());

        // 查询数据
        List<FormulaVO> list = mapper.selectFormulaList(
                param.getName(),
                param.getCategory(),
                param.getEnterpriseType(),
                param.getStatus(),
                param.getVersion()
        );

        // 处理描述字段
        if (!CollectionUtils.isEmpty(list)) {
            for (FormulaVO vo : list) {
                processDescriptions(vo);
            }
        }

        PageInfo<FormulaVO> pageInfo = new PageInfo<>(list);
        log.info("分页查询公式列表完成 - 总数: {}", pageInfo.getTotal());
        return pageInfo;
    }

    /**
     * 获取公式详情
     *
     * @param id 公式ID
     * @return 公式详情
     */
    public FormulaVO getFormulaDetail(Long id) {
        log.info("获取公式详情 - id: {}", id);

        if (id == null) {
            throw new RuntimeException("公式ID不能为空");
        }

        FormulaVO formulaVO = mapper.selectFormulaDetail(id);
        if (formulaVO == null) {
            throw new RuntimeException("公式不存在: " + id);
        }

        // 查询公式变量
        List<FormulaVariableVO> variables = formulaVariableMapper.selectByFormulaId(id);
        formulaVO.setVariables(variables);

        //查询常量
        List<ConstantConfigVO> constants = constantConfigService.getMapper().selectAllConstant();
        formulaVO.setConfigVOs(constants);

        // 处理描述字段
        processDescriptions(formulaVO);

        log.info("获取公式详情完成 - name: {}", formulaVO.getName());
        return formulaVO;
    }

    /**
     * 创建公式
     *
     * @param formulaDTO 公式DTO
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createFormula(FormulaDTO formulaDTO) {
        log.info("创建公式 - name: {}", formulaDTO.getName());

        // 检查名称是否已存在
        if (checkNameExists(formulaDTO.getName(), null)) {
            throw new RuntimeException("公式名称已存在: " + formulaDTO.getName());
        }

        // 验证公式语法
        validateFormula(formulaDTO.getFormula());

        // 创建公式
        Formula formula = new Formula();
        BeanUtils.copyProperties(formulaDTO, formula);
        
        // 设置默认值
        if (formula.getStatus() == null) {
            formula.setStatus(1);
        }
        if (!StringUtils.hasText(formula.getVersion())) {
            formula.setVersion("1.0");
        }
        formula.setUsageCount(0);
        formula.setCreateUser(SysLoginUtils.getUserId());
        formula.setUpdateUser(SysLoginUtils.getUserId());

        int result = this.insertSelective(formula);
        if (result <= 0) {
            throw new RuntimeException("创建公式失败");
        }

        // 保存公式变量
        if (!CollectionUtils.isEmpty(formulaDTO.getVariables())) {
            saveFormulaVariables(formula.getId(), formulaDTO.getVariables());
        }

        log.info("创建公式成功 - id: {}", formula.getId());
        return true;
    }

    /**
     * 更新公式
     *
     * @param id 公式ID
     * @param formulaDTO 公式DTO
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateFormula(Long id, FormulaDTO formulaDTO) {
        log.info("更新公式 - id: {}, name: {}", id, formulaDTO.getName());

        if (id == null) {
            throw new RuntimeException("公式ID不能为空");
        }

        // 检查公式是否存在
        Formula existingFormula = this.selectByPrimaryKey(id);
        if (existingFormula == null) {
            throw new RuntimeException("公式不存在: " + id);
        }

        // 检查名称是否已存在
        if (checkNameExists(formulaDTO.getName(), id)) {
            throw new RuntimeException("公式名称已存在: " + formulaDTO.getName());
        }

        // 验证公式语法
        validateFormula(formulaDTO.getFormula());

        // 更新公式
        Formula formula = new Formula();
        BeanUtils.copyProperties(formulaDTO, formula);
        formula.setId(id);
        formula.setUpdateUser(SysLoginUtils.getUserId());

        int result = this.updateByPrimaryKeySelective(formula);
        if (result <= 0) {
            throw new RuntimeException("更新公式失败");
        }

        // 更新公式变量
        if (!CollectionUtils.isEmpty(formulaDTO.getVariables())) {
            // 先删除原有变量
            formulaVariableMapper.deleteByFormulaId(id);
            // 保存新变量
            saveFormulaVariables(id, formulaDTO.getVariables());
        }

        log.info("更新公式成功 - id: {}", id);
        return true;
    }

    /**
     * 删除公式
     *
     * @param id 公式ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteFormula(Long id) {
        log.info("删除公式 - id: {}", id);

        if (id == null) {
            throw new RuntimeException("公式ID不能为空");
        }

        // 检查公式是否存在
        Formula existingFormula = this.selectByPrimaryKey(id);
        if (existingFormula == null) {
            throw new RuntimeException("公式不存在: " + id);
        }

        int result = this.mapper.deleteByPrimaryKey(id);
        if (result <= 0) {
            throw new RuntimeException("删除公式失败");
        }

        log.info("删除公式成功 - id: {}", id);
        return true;
    }



    /**
     * 计算公式
     *
     * @param calculationDTO 计算DTO
     * @return 计算结果
     */
    public FormulaCalculationResultVO calculateFormula(FormulaCalculationDTO calculationDTO) {
        log.info("计算公式 - formulaId: {}", calculationDTO.getFormulaId());

        // 获取公式详情
        FormulaVO formulaVO = getFormulaDetail(calculationDTO.getFormulaId());

        // 执行计算
        FormulaCalculationResultVO result = formulaCalculationService.calculate(formulaVO, calculationDTO.getVariables(),  null);

        // 更新使用次数和最后测试时间
        mapper.updateUsageCount(calculationDTO.getFormulaId());
        mapper.updateLastTestTime(calculationDTO.getFormulaId());

        log.info("公式计算完成 - result: {}", result.getResult());
        return result;
    }

    /**
     * 验证公式语法
     *
     * @param formula 公式内容
     * @return 是否有效
     */
    public Boolean validateFormula(String formula) {
        log.info("验证公式语法 - formula: {}", formula);

        if (!StringUtils.hasText(formula)) {
            throw new RuntimeException("公式内容不能为空");
        }

        // 调用公式计算服务验证语法
        return formulaCalculationService.validateSyntax(formula);
    }

    /**
     * 检查名称是否存在
     *
     * @param name 公式名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    public Boolean checkNameExists(String name, Long excludeId) {
        if (!StringUtils.hasText(name)) {
            return false;
        }

        int count = mapper.checkNameExists(name, excludeId);
        return count > 0;
    }

    /**
     * 保存公式变量
     *
     * @param formulaId 公式ID
     * @param variableDTOs 变量DTO列表
     */
    private void saveFormulaVariables(Long formulaId, List<FormulaVariableDTO> variableDTOs) {
        List<FormulaVariable> variables = new java.util.ArrayList<>();
        
        for (FormulaVariableDTO dto : variableDTOs) {
            FormulaVariable variable = new FormulaVariable();
            BeanUtils.copyProperties(dto, variable);
            variable.setFormulaId(formulaId);
            variables.add(variable);
        }

        if (!CollectionUtils.isEmpty(variables)) {
            formulaVariableMapper.batchInsert(variables);
        }
    }

    /**
     * 处理描述字段
     *
     * @param vo 公式VO
     */
    private void processDescriptions(FormulaVO vo) {
        // 分类描述
        if (vo.getCategory() != null) {
            vo.setCategoryName(FormulaCategoryEnum.getNameByCode(vo.getCategory()));
        }

        // 状态描述
        if (vo.getStatus() != null) {
            vo.setStatusDesc(vo.getStatus() == 1 ? "启用" : "禁用");
        }

        // 企业类型名称
        if (vo.getEnterpriseType() != null) {
            String typeName = getEnterpriseTypeName(vo.getEnterpriseType());
            vo.setEnterpriseTypeName(typeName);
        }

        // 企业类型列表
        if (StringUtils.hasText(vo.getEnterpriseType())) {
            // 将逗号分隔的字符串转换为列表
            String[] types = vo.getEnterpriseType().split(",");
            ArrayList<String> typeList = new ArrayList<>();
            for (String type : types) {
                if (StringUtils.hasText(type.trim())) {
                    typeList.add(type.trim());
                }
            }
            vo.setEnterpriseTypeList(typeList);
        } else {
            vo.setEnterpriseTypeList(new ArrayList<>());
        }

        // 处理变量描述
        if (!CollectionUtils.isEmpty(vo.getVariables())) {
            for (FormulaVariableVO variable : vo.getVariables()) {
                processVariableDescriptions(variable);
            }
        }
    }

    /**
     * 获取公式计算记录
     *
     * @param formulaId 公式ID
     * @param limit 限制数量
     * @return 计算记录列表
     */
    public List<com.kbao.kbcelms.formula.entity.FormulaCalculationLog> getFormulaCalculationLogs(Long formulaId, Integer limit) {
        log.info("获取公式计算记录 - formulaId: {}, limit: {}", formulaId, limit);

        if (formulaId == null) {
            throw new RuntimeException("公式ID不能为空");
        }

        return formulaCalculationService.getMapper().selectByFormulaId(formulaId, limit);
    }

    /**
     * 获取公式分类选项
     *
     * @return 分类选项列表
     */
    public List<String> getFormulaCategoryList() {
        log.info("获取公式分类选项");

        List<String> categories = new ArrayList<>();
        for (FormulaCategoryEnum category : FormulaCategoryEnum.values()) {
            categories.add(category.getName());
        }

        log.info("获取公式分类选项完成 - 分类数: {}", categories.size());
        return categories;
    }

    /**
     * 处理变量描述字段
     *
     * @param vo 变量VO
     */
    private void processVariableDescriptions(FormulaVariableVO vo) {
        // 变量类型描述
        if (StringUtils.hasText(vo.getType())) {
            switch (vo.getType()) {
                case "number":
                    vo.setTypeDesc("数值");
                    break;
                case "variable":
                    vo.setTypeDesc("变量");
                    break;
                case "constant":
                    vo.setTypeDesc("常数");
                    break;
                default:
                    vo.setTypeDesc(vo.getType());
                    break;
            }
        }
    }

    /**
     * 获取企业类型名称
     *
     * @param enterpriseType 企业类型代码
     * @return 企业类型名称
     */
    private String getEnterpriseTypeName(String enterpriseType) {
        if (enterpriseType == null) {
            return null;
        }

        switch (enterpriseType) {
            case "A":
                return "大型企业";
            case "B":
                return "中型企业";
            case "C":
                return "小型企业";
            default:
                return "未知类型";
        }
    }
}
