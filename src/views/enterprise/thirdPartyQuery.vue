<template>
  <InfoPageLayout
    title="企业信息查看"
    subtitle="企业基本信息、股东信息、财务信息、最终受益人信息"
    icon="el-icon-view"
    :breadcrumbItems="breadcrumbItems"
    @back="handleBack"
  >
      <!-- 企业基本信息卡片 -->
    <InfoCard
      title="企业基本信息"
      icon="el-icon-office-building"
      v-loading="loading"
    >
      <div v-if="basicInfo && basicInfo.id">
        <!-- 基本信息区域 -->
        <div class="info-section">
          <h4 class="section-title">基本信息</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="企业名称" :value="basicInfo.name" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="统一社会信用代码" :value="basicInfo.creditCode" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="法定代表人" :value="basicInfo.legalPersonName" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="企业状态">
                <el-tag :type="getStatusTagType(basicInfo.regStatus)" size="small">
                  {{ basicInfo.regStatus || '-' }}
                </el-tag>
              </InfoItem>
            </el-col>
            <el-col :span="8">
              <InfoItem label="企业类型" :value="basicInfo.companyOrgType" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="法人类型">
                {{ basicInfo.type === 1 ? '人' : basicInfo.type === 2 ? '公司' : '-' }}
              </InfoItem>
            </el-col>
          </el-row>
        </div>

        <!-- 注册信息区域 -->
        <div class="info-section">
          <h4 class="section-title">注册信息</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="注册资本" :value="formatCapital(basicInfo.regCapital, basicInfo.regCapitalCurrency)" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="实收注册资金" :value="formatCapital(basicInfo.actualCapital, basicInfo.actualCapitalCurrency)" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="注册号" :value="basicInfo.regNumber" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="组织机构代码" :value="basicInfo.orgNumber" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="纳税人识别号" :value="basicInfo.taxNumber" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="登记机关" :value="basicInfo.regInstitute" />
            </el-col>
          </el-row>
        </div>

        <!-- 时间信息区域 -->
        <div class="info-section">
          <h4 class="section-title">时间信息</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="成立日期" :value="formatTimestamp(basicInfo.establishTime)" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="核准时间" :value="formatTimestamp(basicInfo.approvedTime)" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="经营开始时间" :value="formatTimestamp(basicInfo.fromTime)" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="经营结束时间" :value="formatTimestamp(basicInfo.toTime)" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="注销日期" :value="formatTimestamp(basicInfo.cancelDate)" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="吊销日期" :value="formatTimestamp(basicInfo.revokeDate)" />
            </el-col>
          </el-row>
        </div>

        <!-- 地址信息区域 -->
        <div class="info-section">
          <h4 class="section-title">地址信息</h4>
          <el-row :gutter="20">
            <el-col :span="24">
              <InfoItem label="注册地址" :value="basicInfo.regLocation" fullWidth />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="省份简称" :value="basicInfo.base" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="市" :value="basicInfo.city" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="区" :value="basicInfo.district" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="行政区划代码" :value="basicInfo.districtCode" />
            </el-col>
          </el-row>
        </div>

        <!-- 行业信息区域 -->
        <div class="info-section">
          <h4 class="section-title">行业信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <InfoItem label="行业" :value="basicInfo.industry" />
            </el-col>
          </el-row>
          <div v-if="basicInfo.industryAll">
            <el-row :gutter="20">
              <el-col :span="6">
                <InfoItem label="行业门类" :value="basicInfo.industryAll.category" />
              </el-col>
              <el-col :span="6">
                <InfoItem label="行业大类" :value="basicInfo.industryAll.categoryBig" />
              </el-col>
              <el-col :span="6">
                <InfoItem label="行业中类" :value="basicInfo.industryAll.categoryMiddle" />
              </el-col>
              <el-col :span="6">
                <InfoItem label="行业小类" :value="basicInfo.industryAll.categorySmall" />
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 经营信息区域 -->
        <div class="info-section">
          <h4 class="section-title">经营信息</h4>
          <el-row :gutter="20">
            <el-col :span="24">
              <InfoItem label="经营范围" :value="basicInfo.businessScope" fullWidth />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="企业规模" :value="basicInfo.scale" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="人员规模" :value="basicInfo.staffNumRange" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="参保人数" :value="basicInfo.socialStaffNum" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="是否小微企业">
                {{ basicInfo.isMicroEnt === 1 ? '是' : basicInfo.isMicroEnt === 0 ? '否' : '-' }}
              </InfoItem>
            </el-col>
          </el-row>
        </div>

        <!-- 联系信息区域 -->
        <div class="info-section">
          <h4 class="section-title">联系信息</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="企业联系方式" :value="basicInfo.phoneNumber" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="邮箱" :value="basicInfo.email" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="网址" :value="basicInfo.websiteList" />
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="basicInfo.emailList && basicInfo.emailList.length">
            <el-col :span="24">
              <InfoItem label="全部邮箱列表" fullWidth>
                <el-tag v-for="email in basicInfo.emailList" :key="email" size="small" style="margin-right: 8px;">
                  {{ email }}
                </el-tag>
              </InfoItem>
            </el-col>
          </el-row>
        </div>

        <!-- 其他信息区域 -->
        <div class="info-section">
          <h4 class="section-title">其他信息</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="简称" :value="basicInfo.alias" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="英文名" :value="basicInfo.property3" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="企业评分" :value="basicInfo.percentileScore ? `${basicInfo.percentileScore}/10000` : '-'" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="曾用名" :value="basicInfo.historyNames" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="企业标签" :value="basicInfo.tags" />
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="basicInfo.historyNameList && basicInfo.historyNameList.length">
            <el-col :span="24">
              <InfoItem label="曾用名列表" fullWidth>
                <el-tag v-for="name in basicInfo.historyNameList" :key="name" size="small" style="margin-right: 8px;">
                  {{ name }}
                </el-tag>
              </InfoItem>
            </el-col>
          </el-row>
        </div>
      </div>
      <div v-else-if="!loading" class="no-data">
        <el-empty description="暂无企业基本信息"></el-empty>
      </div>
    </InfoCard>

      <!-- 股东信息 -->
    <InfoCard title="股东信息" icon="el-icon-user" v-loading="loading">
      <div v-if="shareholderList && shareholderList.length">
        <div v-for="(shareholder, index) in shareholderList" :key="shareholder.id || index" class="shareholder-item">
          <div class="shareholder-header">
            <h5>{{ shareholder.name || '-' }}</h5>
            <el-tag size="small" :type="getShareholderTypeTag(shareholder.type)">
              {{ getShareholderTypeText(shareholder.type) }}
            </el-tag>
          </div>

          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="股东名称" :value="shareholder.name" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="简称" :value="shareholder.alias" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="首次持股日期" :value="formatTimestamp(shareholder.ftShareholding)" />
            </el-col>
          </el-row>

          <!-- 认缴信息 -->
          <div v-if="shareholder.capital && shareholder.capital.length" class="capital-section">
            <h6>认缴信息</h6>
            <el-table :data="shareholder.capital" size="small" class="modern-table">
              <el-table-column prop="amomon" label="出资金额" width="150" />
              <el-table-column prop="payment" label="认缴方式" width="150" />
              <el-table-column prop="time" label="出资时间" width="150" />
              <el-table-column prop="percent" label="占比" width="100" />
            </el-table>
          </div>

          <!-- 实缴信息 -->
          <div v-if="shareholder.capitalActl && shareholder.capitalActl.length" class="capital-section">
            <h6>实缴信息</h6>
            <el-table :data="shareholder.capitalActl" size="small" class="modern-table">
              <el-table-column prop="amomon" label="出资金额" width="150" />
              <el-table-column prop="payment" label="认缴方式" width="150" />
              <el-table-column prop="time" label="出资时间" width="150" />
              <el-table-column prop="percent" label="占比" width="100" />
            </el-table>
          </div>

          <el-divider v-if="index < shareholderList.length - 1"></el-divider>
        </div>
      </div>
      <div v-else-if="!loading" class="no-data">
        <el-empty description="暂无股东信息"></el-empty>
      </div>
    </InfoCard>

      <!-- 最终受益人信息 -->
    <InfoCard title="最终受益人信息" icon="el-icon-s-custom" v-loading="loading">
      <div v-if="beneficiaryList && beneficiaryList.length">
        <div v-for="(beneficiary, index) in beneficiaryList" :key="beneficiary.id || index" class="beneficiary-item">
          <div class="beneficiary-header">
            <h5>{{ beneficiary.name || '-' }}</h5>
            <el-tag size="small" :type="beneficiary.type === 'human' ? 'success' : 'primary'">
              {{ beneficiary.type === 'human' ? '个人' : beneficiary.type === 'company' ? '公司' : beneficiary.type }}
            </el-tag>
          </div>

          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="姓名" :value="beneficiary.name" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="占比" :value="beneficiary.percent" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="类型" :value="beneficiary.type" />
            </el-col>
          </el-row>

          <!-- 股权路径信息 -->
          <div v-if="beneficiary.chainList && beneficiary.chainList.length" class="chain-section">
            <h6>股权路径</h6>
            <div v-for="(chainGroup, groupIndex) in beneficiary.chainList" :key="groupIndex" class="chain-group">
              <div class="chain-path">
                <div v-for="(chainItem, itemIndex) in chainGroup" :key="itemIndex" class="chain-item">
                  <div class="chain-node">
                    <el-tag size="mini" :type="getChainTypeTag(chainItem.type)">
                      {{ chainItem.title || chainItem.type }}
                    </el-tag>
                    <div class="chain-value">{{ chainItem.value }}</div>
                    <div v-if="chainItem.info" class="chain-info">{{ chainItem.info }}</div>
                  </div>
                  <div v-if="itemIndex < chainGroup.length - 1" class="chain-arrow">
                    <i class="el-icon-right"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <el-divider v-if="index < beneficiaryList.length - 1"></el-divider>
        </div>
      </div>
      <div v-else-if="!loading" class="no-data">
        <el-empty description="暂无最终受益人信息"></el-empty>
      </div>
    </InfoCard>

      <!-- 财务信息 -->
    <InfoCard title="财务信息" icon="el-icon-s-finance" v-loading="loading">
      <div v-if="financialList && financialList.length">
        <div v-for="(financial, index) in financialList" :key="financial.id || index" class="financial-item">
          <div class="financial-overview">
            <el-row :gutter="20">
              <el-col :span="8">
                <InfoItem label="总数" :value="financial.total" />
              </el-col>
            </el-row>
          </div>

          <!-- 各项财务指标 -->
          <div class="financial-indicators">
            <!-- 净资产信息 -->
            <div v-if="financial.netAssets" class="indicator-section">
              <h6>{{ financial.netAssets.title || '净资产信息' }}</h6>
              <p class="indicator-info">{{ financial.netAssets.info }}</p>
              <div v-if="financial.netAssets.list && financial.netAssets.list.length">
                <el-table :data="financial.netAssets.list" size="small" class="modern-table">
                  <el-table-column prop="year" label="年份" width="100" />
                  <el-table-column prop="amount" label="金额" width="150" />
                  <el-table-column prop="convertAmount" label="带单位金额" width="150" />
                </el-table>
              </div>
            </div>

            <!-- 总资产信息 -->
            <div v-if="financial.totalAssets" class="indicator-section">
              <h6>{{ financial.totalAssets.title || '总资产信息' }}</h6>
              <p class="indicator-info">{{ financial.totalAssets.info }}</p>
              <div v-if="financial.totalAssets.list && financial.totalAssets.list.length">
                <el-table :data="financial.totalAssets.list" size="small" class="modern-table">
                  <el-table-column prop="year" label="年份" width="100" />
                  <el-table-column prop="amount" label="金额" width="150" />
                  <el-table-column prop="convertAmount" label="带单位金额" width="150" />
                </el-table>
              </div>
            </div>

            <!-- 营业收入信息 -->
            <div v-if="financial.businessIncome" class="indicator-section">
              <h6>{{ financial.businessIncome.title || '营业收入信息' }}</h6>
              <p class="indicator-info">{{ financial.businessIncome.info }}</p>
              <div v-if="financial.businessIncome.list && financial.businessIncome.list.length">
                <el-table :data="financial.businessIncome.list" size="small" class="modern-table">
                  <el-table-column prop="year" label="年份" width="100" />
                  <el-table-column prop="amount" label="金额" width="150" />
                  <el-table-column prop="convertAmount" label="带单位金额" width="150" />
                </el-table>
              </div>
            </div>

            <!-- 净利润信息 -->
            <div v-if="financial.netProfit" class="indicator-section">
              <h6>{{ financial.netProfit.title || '净利润信息' }}</h6>
              <p class="indicator-info">{{ financial.netProfit.info }}</p>
              <div v-if="financial.netProfit.list && financial.netProfit.list.length">
                <el-table :data="financial.netProfit.list" size="small" class="modern-table">
                  <el-table-column prop="year" label="年份" width="100" />
                  <el-table-column prop="amount" label="金额" width="150" />
                  <el-table-column prop="convertAmount" label="带单位金额" width="150" />
                </el-table>
              </div>
            </div>

            <!-- 净利率信息 -->
            <div v-if="financial.netInterestRate" class="indicator-section">
              <h6>{{ financial.netInterestRate.title || '净利率信息' }}</h6>
              <p class="indicator-info">{{ financial.netInterestRate.info }}</p>
              <div v-if="financial.netInterestRate.list && financial.netInterestRate.list.length">
                <el-table :data="financial.netInterestRate.list" size="small" class="modern-table">
                  <el-table-column prop="year" label="年份" width="100" />
                  <el-table-column prop="amount" label="金额" width="150" />
                  <el-table-column prop="convertAmount" label="带单位金额" width="150" />
                </el-table>
              </div>
            </div>

            <!-- 毛利率信息 -->
            <div v-if="financial.grossInterestRate" class="indicator-section">
              <h6>{{ financial.grossInterestRate.title || '毛利率信息' }}</h6>
              <p class="indicator-info">{{ financial.grossInterestRate.info }}</p>
              <div v-if="financial.grossInterestRate.list && financial.grossInterestRate.list.length">
                <el-table :data="financial.grossInterestRate.list" size="small" class="modern-table">
                  <el-table-column prop="year" label="年份" width="100" />
                  <el-table-column prop="amount" label="金额" width="150" />
                  <el-table-column prop="convertAmount" label="带单位金额" width="150" />
                </el-table>
              </div>
            </div>
          </div>

          <el-divider v-if="index < financialList.length - 1"></el-divider>
        </div>
      </div>
      <div v-else-if="!loading" class="no-data">
        <el-empty description="暂无财务信息"></el-empty>
      </div>
    </InfoCard>
  </InfoPageLayout>
</template>

<script>
import InfoPageLayout from '@/components/layouts/InfoPageLayout'
import InfoCard from '@/components/layouts/InfoCard'
import InfoItem from '@/components/layouts/InfoItem'
import { getEnterpriseFullInfo } from '@/api/enterprise'

export default {
  name: 'EnterpriseInfoView',
  components: {
    InfoPageLayout,
    InfoCard,
    InfoItem
  },
  data() {
    return {
      loading: false,
      breadcrumbItems: [
        { text: '企业信息管理', icon: 'el-icon-office-building', to: 'EnterpriseManagement' },
        { text: '企业信息查看', icon: 'el-icon-view' }
      ],
      basicInfo: null,
      shareholderList: [],
      financialList: [],
      beneficiaryList: []
    }
  },
  created() {
    // 从路由参数获取企业信用代码
    const { creditCode } = this.$route.params
    if (creditCode) {
      this.loadEnterpriseData(creditCode)
    } else {
      this.$message.error('企业信用代码不能为空')
      this.$router.back()
    }
  },
  methods: {
    async loadEnterpriseData(creditCode) {
      this.loading = true
      try {
        const data = await getEnterpriseFullInfo(creditCode)
        console.log('API响应:', data)

          this.basicInfo = data.basicInfo
          this.shareholderList = data.shareholderList || []
          this.financialList = data.financialList || []
          this.beneficiaryList = data.beneficiaryList || []

          console.log('设置的数据:', {
            basicInfo: this.basicInfo,
            shareholderList: this.shareholderList,
            financialList: this.financialList,
            beneficiaryList: this.beneficiaryList
          })
      } catch (error) {
        console.error('加载企业信息失败:', error)
        this.$message.error('加载企业信息失败')
      } finally {
        this.loading = false
      }
    },

    // 格式化时间戳
    formatTimestamp(timestamp) {
      if (!timestamp) return '-'
      return new Date(timestamp).toLocaleDateString()
    },

    // 格式化注册资本
    formatCapital(capital, currency) {
      if (!capital) return '-'
      return currency ? `${capital} ${currency}` : capital
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '存续': 'success',
        '在业': 'success',
        '注销': 'info',
        '吊销': 'danger',
        '迁出': 'warning'
      }
      return statusMap[status] || 'info'
    },

    // 获取股东类型标签
    getShareholderTypeTag(type) {
      const typeMap = {
        1: 'primary',  // 公司
        2: 'success',  // 人
        3: 'warning'   // 其它
      }
      return typeMap[type] || 'info'
    },

    // 获取股东类型文本
    getShareholderTypeText(type) {
      const typeMap = {
        1: '公司',
        2: '人',
        3: '其它'
      }
      return typeMap[type] || '-'
    },

    // 获取链条类型标签
    getChainTypeTag(type) {
      const typeMap = {
        'human': 'success',
        'company': 'primary',
        'title': 'info',
        'percent': 'warning'
      }
      return typeMap[type] || 'info'
    },

    handleBack() {
      this.$router.back()
    }
  }
}
</script>

<style lang="less" scoped>
.modern-table {
  /deep/ .el-table__header-wrapper {
    th {
      background: #f7ecdd;
      font-weight: 600;
      color: #2c3e50;
    }
  }
}

.info-section {
  margin-bottom: 24px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e1f5fe;
  }
}

.shareholder-item {
  margin-bottom: 24px;

  .shareholder-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h5 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
    }
  }

  .capital-section {
    margin-top: 16px;

    h6 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #666;
    }
  }
}

.financial-item {
  margin-bottom: 24px;

  .financial-overview {
    margin-bottom: 20px;
  }

  .financial-indicators {
    .indicator-section {
      margin-bottom: 20px;

      h6 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 600;
        color: #2c3e50;
      }

      .indicator-info {
        margin: 0 0 12px 0;
        font-size: 12px;
        color: #666;
      }
    }
  }
}

.beneficiary-item {
  margin-bottom: 24px;

  .beneficiary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h5 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
    }
  }

  .chain-section {
    margin-top: 16px;

    h6 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #666;
    }

    .chain-group {
      margin-bottom: 12px;

      .chain-path {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .chain-item {
          display: flex;
          align-items: center;

          .chain-node {
            text-align: center;
            padding: 8px;
            border: 1px solid #e1f5fe;
            border-radius: 4px;
            background: #f8f9fa;
            min-width: 120px;

            .chain-value {
              margin-top: 4px;
              font-size: 12px;
              font-weight: 600;
              color: #2c3e50;
            }

            .chain-info {
              margin-top: 2px;
              font-size: 10px;
              color: #666;
            }
          }

          .chain-arrow {
            margin: 0 8px;
            color: #666;
          }
        }
      }
    }
  }
}

.beneficiary-overview {
  margin-bottom: 20px;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}
</style>
