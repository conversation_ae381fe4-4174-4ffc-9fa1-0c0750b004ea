package com.kbao.kbcelms.genAgentEnterprise.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListReqVo;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListResVo;import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import org.apache.ibatis.annotations.Mapper;import org.apache.ibatis.annotations.Param;import java.util.List;

/**
 * <p>
 * 顾问企业表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Mapper
public interface GenAgentEnterpriseMapper extends BaseMapper<GenAgentEnterprise, Integer> {

    List<AgentEnterpriseListResVo> getAgentEnterpriseList(AgentEnterpriseListReqVo reqVo);

    List<GenAgentEnterprise> getEnterpriseListByAgentCode(AgentEnterpriseListReqVo reqVo);

    /**
     * 根据统一信用代码查询企业信息
     */
    GenAgentEnterprise getByCreditCode(@Param("creditCode") String creditCode);
}
