package com.kbao.kbcelms.formula.bean;

import com.kbao.kbcelms.questionnaire.entity.QuestionnaireAnswer;
import com.kbao.kbcelms.riskMatrix.vo.RiskMatrixDetailVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 评分项计算请求
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScoreItemCalculationRequest {

    /**
     * 评分项详情
     */
    private RiskMatrixDetailVO.ScoreItemDetailVO item;

    /**
     * 答案映射 (评分项ID -> 问卷答案)
     */
    private Map<Long, QuestionnaireAnswer> answerMap;

    /**
     * 额外的计算参数（可选）
     */
    private Map<String, Object> extraParams;

    /**
     * 构造函数（简化版）
     */
    public ScoreItemCalculationRequest(RiskMatrixDetailVO.ScoreItemDetailVO item, 
                                      Map<Long, QuestionnaireAnswer> answerMap) {
        this.item = item;
        this.answerMap = answerMap;
    }
}
