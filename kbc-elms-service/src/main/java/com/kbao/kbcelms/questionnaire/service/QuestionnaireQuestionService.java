package com.kbao.kbcelms.questionnaire.service;

import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.questionnaire.bean.QuestionnaireQuestionSaveBean;
import com.kbao.kbcelms.questionnaire.bean.QuestionnaireQuestionOptionSaveBean;
import com.kbao.kbcelms.questionnaire.dao.QuestionnaireQuestionMapper;
import com.kbao.kbcelms.questionnaire.entity.QuestionnaireQuestion;
import com.kbao.kbcelms.questionnaire.vo.QuestionnaireQuestionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import com.kbao.kbcelms.questionnaire.bean.QuestionSortBean;
import com.kbao.tool.util.SysLoginUtils;

/**
 * 问卷问题服务实现类
 */
@Slf4j
@Service
public class QuestionnaireQuestionService extends BaseSQLServiceImpl<QuestionnaireQuestion, Long, QuestionnaireQuestionMapper> {

    @Autowired
    private QuestionnaireQuestionOptionService questionnaireQuestionOptionService;

    /**
     * 根据问卷ID查询问题列表（包含选项）
     */
    public List<QuestionnaireQuestionVO> getQuestionsByQuestionnaireId(Long questionnaireId) {
        try {
            if (questionnaireId == null) {
                throw new IllegalArgumentException("问卷ID不能为空");
            }

            return this.mapper.selectQuestionsByQuestionnaireId(questionnaireId);
        } catch (Exception e) {
            log.error("查询问题列表失败，questionnaireId: {}", questionnaireId, e);
            throw new RuntimeException("查询失败");
        }
    }

    /**
     * 批量删除问题（逻辑删除）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteByIds(List<Long> ids) {
        try {
            if (CollectionUtils.isEmpty(ids)) {
                return true;
            }

            int result = this.mapper.batchDeleteByIds(ids);
            
            if (result > 0) {
                // 删除相关选项
                for (Long id : ids) {
                    questionnaireQuestionOptionService.deleteByQuestionId(id);
                }
            }
            
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除问题失败，ids: {}", ids, e);
            throw new RuntimeException("删除失败");
        }
    }

    /**
     * 根据问卷ID删除所有问题（逻辑删除）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByQuestionnaireId(Long questionnaireId) {
        try {
            if (questionnaireId == null) {
                throw new IllegalArgumentException("问卷ID不能为空");
            }

            // 先查询所有问题ID
            List<QuestionnaireQuestionVO> questions = getQuestionsByQuestionnaireId(questionnaireId);
            if (!CollectionUtils.isEmpty(questions)) {
                List<Long> questionIds = questions.stream()
                        .map(QuestionnaireQuestionVO::getId)
                        .collect(Collectors.toList());
                
                // 删除所有选项
                questionnaireQuestionOptionService.deleteByQuestionIds(questionIds);
            }

            int result = this.mapper.deleteByQuestionnaireId(questionnaireId);
            return result > 0;
        } catch (Exception e) {
            log.error("删除问卷问题失败，questionnaireId: {}", questionnaireId, e);
            throw new RuntimeException("删除失败");
        }
    }

    /**
     * 获取问卷的最大排序号
     */
    public Integer getMaxSortOrder(Long questionnaireId) {
        try {
            if (questionnaireId == null) {
                throw new IllegalArgumentException("问卷ID不能为空");
            }

            return this.mapper.getMaxSortOrder(questionnaireId);
        } catch (Exception e) {
            log.error("获取最大排序号失败，questionnaireId: {}", questionnaireId, e);
            throw new RuntimeException("查询失败");
        }
    }

    /**
     * 保存问题和选项
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveQuestionsAndOptions(Long questionnaireId, List<QuestionnaireQuestionSaveBean> questions) {
        if (CollectionUtils.isEmpty(questions)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        
        for (int i = 0; i < questions.size(); i++) {
            QuestionnaireQuestionSaveBean questionBean = questions.get(i);
            
            // 保存问题
            QuestionnaireQuestion question = new QuestionnaireQuestion();
            BeanUtils.copyProperties(questionBean, question);
            question.setQuestionnaireId(questionnaireId);
            question.setSortOrder(i + 1);
            question.setCreateTime(now);
            question.setUpdateTime(now);
            question.setDeleted(0);
            
            if (questionBean.getId() == null) {
                this.mapper.insert(question);
            } else {
                this.updateByPrimaryKeySelective(question);
                // 删除原有选项
                questionnaireQuestionOptionService.deleteByQuestionId(question.getId());
            }
            
            // 保存选项
            if (!CollectionUtils.isEmpty(questionBean.getOptions())) {
                questionnaireQuestionOptionService.saveOptions(question.getId(), questionBean.getOptions());
            }
        }
    }

    /**
     * 转换为问题保存Bean
     */
    public QuestionnaireQuestionSaveBean convertToQuestionSaveBean(QuestionnaireQuestionVO questionVO) {
        QuestionnaireQuestionSaveBean saveBean = new QuestionnaireQuestionSaveBean();
        BeanUtils.copyProperties(questionVO, saveBean);
        
        if (!CollectionUtils.isEmpty(questionVO.getOptions())) {
            List<QuestionnaireQuestionOptionSaveBean> options = questionVO.getOptions().stream()
                    .map(optionVO -> {
                        QuestionnaireQuestionOptionSaveBean optionSaveBean = new QuestionnaireQuestionOptionSaveBean();
                        BeanUtils.copyProperties(optionVO, optionSaveBean);
                        return optionSaveBean;
                    })
                    .collect(Collectors.toList());
            saveBean.setOptions(options);
        }
        
        return saveBean;
    }






    /**
     * 保存单个题目
     */
    @Transactional(rollbackFor = Exception.class)
    public QuestionnaireQuestionVO saveQuestion(Long questionnaireId, QuestionnaireQuestionSaveBean questionBean) {
        try {
            if (questionnaireId == null) {
                throw new IllegalArgumentException("问卷ID不能为空");
            }
            if (questionBean == null) {
                throw new IllegalArgumentException("题目信息不能为空");
            }

            LocalDateTime now = LocalDateTime.now();
            String currentUser = SysLoginUtils.getUserId();

            QuestionnaireQuestion question = new QuestionnaireQuestion();
            BeanUtils.copyProperties(questionBean, question);
            question.setQuestionnaireId(questionnaireId);
            question.setCreateTime(now);
            question.setUpdateTime(now);
            question.setDeleted(0);

            if (questionBean.getId() == null) {
                // 新增题目
                // 获取最大排序号
                Integer maxSortOrder = this.mapper.getMaxSortOrder(questionnaireId);
                question.setSortOrder(maxSortOrder == null ? 1 : maxSortOrder + 1);

                this.mapper.insert(question);
                log.info("新增题目成功，ID: {}", question.getId());
            } else {
                // 更新题目
                question.setId(questionBean.getId());
                this.updateByPrimaryKeySelective(question);
                log.info("更新题目成功，ID: {}", question.getId());

                // 删除原有选项
                questionnaireQuestionOptionService.deleteByQuestionId(question.getId());
            }

            // 保存选项
            if (!CollectionUtils.isEmpty(questionBean.getOptions())) {
                questionnaireQuestionOptionService.saveOptions(question.getId(), questionBean.getOptions());
            }

            // 构建返回对象
            QuestionnaireQuestionVO result = new QuestionnaireQuestionVO();
            BeanUtils.copyProperties(question, result);
            result.setOptionCount(questionBean.getOptions() != null ? questionBean.getOptions().size() : 0);

            return result;
        } catch (Exception e) {
            log.error("保存题目失败，questionnaireId: {}, questionBean: {}", questionnaireId, questionBean, e);
            throw new RuntimeException("保存失败");
        }
    }

    /**
     * 删除单个题目
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteQuestion(Long questionId) {
        try {
            if (questionId == null) {
                throw new IllegalArgumentException("题目ID不能为空");
            }

            // 逻辑删除题目
            QuestionnaireQuestion question = new QuestionnaireQuestion();
            question.setId(questionId);
            question.setDeleted(1);
            question.setUpdateTime(LocalDateTime.now());

            int result = this.updateByPrimaryKeySelective(question);

            if (result > 0) {
                // 删除相关选项
                questionnaireQuestionOptionService.deleteByQuestionId(questionId);
                log.info("删除题目成功，ID: {}", questionId);
            }

            return result > 0;
        } catch (Exception e) {
            log.error("删除题目失败，questionId: {}", questionId, e);
            throw new RuntimeException("删除失败");
        }
    }

    /**
     * 更新题目排序
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateQuestionSort(List<QuestionSortBean.QuestionSortItem> questions) {
        try {
            if (CollectionUtils.isEmpty(questions)) {
                return true;
            }

            LocalDateTime now = LocalDateTime.now();

            for (QuestionSortBean.QuestionSortItem item : questions) {
                if (item.getId() != null && item.getSortOrder() != null) {
                    QuestionnaireQuestion question = new QuestionnaireQuestion();
                    question.setId(item.getId());
                    question.setSortOrder(item.getSortOrder());
                    question.setUpdateTime(now);

                    this.updateByPrimaryKeySelective(question);
                }
            }

            log.info("更新题目排序成功，题目数量: {}", questions.size());
            return true;
        } catch (Exception e) {
            log.error("更新题目排序失败，questions: {}", questions, e);
            throw new RuntimeException("排序更新失败");
        }
    }
}
