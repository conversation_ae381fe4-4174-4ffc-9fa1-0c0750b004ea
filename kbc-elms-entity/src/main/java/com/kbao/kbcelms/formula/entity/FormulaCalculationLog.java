package com.kbao.kbcelms.formula.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 公式计算记录实体类
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(value = "FormulaCalculationLog", description = "公式计算记录实体")
public class FormulaCalculationLog {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 公式ID
     */
    @ApiModelProperty(value = "公式ID")
    private Long formulaId;

    /**
     * 公式名称
     */
    @ApiModelProperty(value = "公式名称")
    private String formulaName;

    /**
     * 输入变量（JSON格式）
     */
    @ApiModelProperty(value = "输入变量")
    private String inputVariables;

    /**
     * 计算结果
     */
    @ApiModelProperty(value = "计算结果")
    private BigDecimal calculationResult;

    /**
     * 计算时间
     */
    @ApiModelProperty(value = "计算时间")
    private LocalDateTime calculationTime;

    /**
     * 执行时间（毫秒）
     */
    @ApiModelProperty(value = "执行时间（毫秒）")
    private Integer executionTimeMs;

    /**
     * 计算状态：1-成功，0-失败
     */
    @ApiModelProperty(value = "计算状态：1-成功，0-失败")
    private Integer status;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
}
