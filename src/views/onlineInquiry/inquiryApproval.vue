<template>
  <div class="inquiry-approval">

    <el-breadcrumb class="breadcrumb dt-bread" separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: 'inquiryApprovalList' }"> 询价产品上报审批 </el-breadcrumb-item>
      <el-breadcrumb-item :style="{ color: themeObj.color }">
        产品上报审批
      </el-breadcrumb-item>
    </el-breadcrumb>

    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool" class="log-tool"></TableToolTemp>

    <div style="padding: 0 30px;">
      <div>
    <el-form class="approvalFormClass" :model="approvalData" ref="userForm" label-width="150px" label-position="left">
      <el-form-item label="保险公司" prop="userName">
        <el-select v-model="approvalData.inquiryCompany" placeholder="请选择保险公司" class="full-width" disabled>
          <el-option v-for="template in inquiryCompanyList" :key="template.dicItemCode" :label="template.dicItemName" :value="template.dicItemName"/>
        </el-select>
      </el-form-item>
      <el-form-item label="上报产品" prop="nickName">
        <el-table :data="productData" class="dt-table" style="width: 80%" v-hover border>
          <el-table-column align="center" label="产品大类">
            <template slot-scope="scope">
              <el-select v-model="scope.row.productCategoryCode" placeholder="请选择产品大类" disabled>
                <el-option v-for="item in productCategoryList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" label="产品名称">
            <template slot-scope="scope">
              <el-select v-model="scope.row.productCode" placeholder="请选择产品名称" disabled>
                <el-option v-for="item in productNameList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="handlingFee" label="手续费/经纪费">
            <template slot-scope="scope">
              <el-input v-model="scope.row.handlingFee" placeholder="请输入手续费比例" style="width: 150px" disabled></el-input> %
            </template>
          </el-table-column>
        </el-table>

      </el-form-item>
      <el-form-item label="上报机构">
        <template>
          {{ approvalData.reportCompany }}
        </template>
      </el-form-item>
      <el-form-item label="上报人" prop="roleType">
        <template>
          {{ approvalData.reportor }}
        </template>
      </el-form-item>
      <el-form-item label="产品方案/形态" prop="phone">
        <div class="product-item" v-for="(item, index) in productPlanList" :key="index">
          <div style="width: 60%; padding-left:10px;">
            <span>{{item.fileName}}</span>
          </div>
          <div style="width: 35%;text-align: right">
            <i class="el-icon-download"></i>
            <el-button type="text" @click="download(item)" style="padding-left: 5px;">下载</el-button>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="备注" prop="stopReason">
        <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 8}" placeholder="请输入上报备注" v-model="approvalData.notes"
                  style="width: 80%" disabled></el-input>
      </el-form-item>
    </el-form>
      </div>
    <div class="footerBtn">
      <el-button class="btn-center" @click="giveBack" type="primary" style="width: 150px">退回</el-button>
      <el-button class="btn-center" @click="pass" type="primary" plain :style="{ color: themeObj.color,width: '150px' }">审批通过</el-button>
    </div>
    </div>


    <!-- 退回弹窗 -->
    <DtPopup :isShow.sync="giveBackPopup" center width="600px" :footer="false" title="退回" class="user-match" @close="cancelGiveBack">
      <el-form ref="giveBackForm" class="channelFormClass" :model="giveBackData" :rules="giveBackRules" label-width="110px" label-position="left">
         <el-form-item label="退回原因" prop="giveBackReason">
           <el-input v-model="giveBackData.giveBackReason" type="textarea" class="dt-input-width" :rows="4" :maxlength="200" placeholder="退回原因（必填）" maxlength="150字" show-word-limit required></el-input>
         </el-form-item>
          <el-form-item>
            <div style="padding:20px 0;">
              <el-button type="primary" class="btn-width" :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px',width:'150px' }" @click="cancelGiveBack">取 消
              </el-button>
              <el-button type="primary" class="btn-width" :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color,width:'150px' }" @click="confirmGiveBack">确认退回
              </el-button>
            </div>
          </el-form-item>
      </el-form>
    </DtPopup>

  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import * as api from "@/api/roleManagement/index.js";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import {findLegalOrgData, getCurrentUserTenantInfo} from "@/api/userManagement";

export default {
  name: "inquiryApproval",
  data() {
    return {
      toolListProps: {
        toolTitle: "产品上报审批",
        toolList: []
      },
      tableData: [],
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          roleName: ""
        }
      },
      searchFormTemp: [
        {
          label: "保险公司",
          name: "roleName",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "上报机构",
          name: "roleType",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "审批状态",
          name: "roleType",
          type: "select",
          width: "200px",
          list:[]
        }
      ],
      total: 0,
      legalList:[],
      inquiryCompanyList:[],
      productCategoryList:[],
      productNameList:[],
      productData:[],
      productPlanList:[],
      approvalData: {
        inquiryCompany: "",
        reportCompany: "",
        reportor: "",
        notes: "",
      },
      giveBackPopup: false,
      giveBackRules: {giveBackReason: [{required: true, validator: validate, trigger: "blur"}]},
      giveBackData: {
        approvalId: "",
        giveBackReason: ""
      }
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    }
  },
  filters: {
    getRoleType(list, roleType) {
      let index = list.findIndex((v) => v.dicItemCode == roleType);
      return index > -1 ? list[index].dicItemName : "-";
    }
  },
  async created() {
    await this.getDicFun();
    await this.initData();
  },
  methods: {
    async getDicFun() {
      // 询价公司
      this.inquiryCompanyList = await getDicItemList("elms.inquiry.company");
      console.log(this.$route.query.approvalId,'------------------approvalId-------------------');

      this.productCategoryList = [
        {"value":"产品大类1-value","label":"产品大类1"},
        {"value":"产品大类2-value","label":"产品大类2"},
        {"value":"产品大类3-value","label":"产品大类3"},
        {"value":"产品大类4-value","label":"产品大类4"},
        {"value":"产品大类5-value","label":"产品大类5"},
      ];
    },
    async initData() {

      // this.tableData = [
      //   {"companyName":"平安财险","productCategory":"企财险","productName":"财产一切险","handlingFee":"15","productSolution":"平安财产一切险产品详情.pdf","reportingAgency":"上海分公司","businessName":"大童经纪","reporter":"上报人","approvalStatus":"1"},
      //   {"companyName":"平安财险","productCategory":"企财险","productName":"财产一切险","handlingFee":"15","productSolution":"平安财产一切险产品详情.pdf","reportingAgency":"上海分公司","businessName":"大童经纪","reporter":"上报人","approvalStatus":"2"},
      //   {"companyName":"平安财险","productCategory":"企财险","productName":"财产一切险","handlingFee":"15","productSolution":"平安财产一切险产品详情.pdf","reportingAgency":"上海分公司","businessName":"大童经纪","reporter":"上报人","approvalStatus":"0"},
      //   {"companyName":"平安财险","productCategory":"企财险","productName":"财产一切险","handlingFee":"15","productSolution":"平安财产一切险产品详情.pdf","reportingAgency":"上海分公司","businessName":"大童经纪","reporter":"上报人","approvalStatus":"2"}
      // ];
      // this.total = this.tableData.length;

      let res = await getCurrentUserTenantInfo();
      if(res) {
        this.approvalData.reportor = res.userName;
        this.approvalData.reportCompany = res.orgName;
      }

      this.approvalData.inquiryCompany = "平安财险";
      this.approvalData.notes = "目前，[公司 / 单位] 在 [财产安全 / 员工保障 / 业务运营等方面] 存在一定风险隐患。例如，[具体说明风险情况，如公司固定资产面临自然灾害或意外事故损失风险、员工在工作中可能遭遇意外伤害、业务开展过程中可能因责任问题引发纠纷等]。为有效转移和规避这些风险，降低因意外事件造成的经济损失，保障公司正常运营和员工合法权益，采购相应的保险产品十分必要。";

      this.productData.push({index:1,productCategoryCode:"产品大类1-value",productCode:"产品1-产品大类1-value",handlingFee:"333"});

      this.productPlanList = [
        {
          'fileUrl': 'https://www-sta.kbao123.com/gateway/oss2/kbc-ufs-sta/T0001/B00008/T0001/logo/2025/01/23/4e62d8/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250123110249.png?Expires=2368321430&OSSAccessKeyId=LTAI4GJaRdNjjXCwyh28M7Vr&Signature=rY04uEOiG3DUGKQc%2FSrlGp9xABk%3D',
          'fileName': '文件文件名称1'
        },
        {
          'fileUrl': 'https://www-sta.kbao123.com/gateway/oss2/kbc-ufs-sta/T0001/B00008/T0001/logo/2024/01/26/ec9641/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20240126161813.png?Expires=2336977111&OSSAccessKeyId=LTAI4GJaRdNjjXCwyh28M7Vr&Signature=JzUNl%2FJpL%2Bk0Hr%2BelrCiZFohNdg%3D',
          'fileName': '文件文件名称2'
        }
      ];

      // this.initParam.param.tenantId = this.$store.state.layoutStore.currentLoginUser.tenantId;
      // let res = await api.getRolePage(this.initParam);
      // if (res) {
      //   this.total = res.total;
      //   this.tableData = [];
      //   if (res.list) {
      //     this.tableData = res.list ? res.list : [{}];
      //   }
      // }
    },
    handleTool(item) {
    },
    download(item){
      let link = document.createElement("a");
      link.setAttribute("download", item.fileName);
      link.href = item.fileUrl;
      link.click();
    },
    closePopup() {
      this.addData = _.cloneDeep(this.$options.data().addData);
      this.showPopup = false;
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate();
      });
    },
    giveBack(){
      this.giveBackPopup = true;
    },
    pass(){},
    cancelGiveBack(){
      this.giveBackPopup = false;
    },
    confirmGiveBack(){
      if (!validateAlls(this.$refs.giveBackForm)) { return; }
      this.giveBackPopup = false;
    },
    // 搜索
    normalSearch(data) {
      this.initParam = data;
      this.initData();
    },
    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.initData();
    },
    update(row) {
      this.isAdd = false;
      this.addData.roleId = row.id;
      this.addData.roleName = row.roleName;
      this.addData.roleDesc = row.roleDesc;
      this.addData.roleType = row.roleType;
      this.showPopup = true;
    },
    approval(row){
      this.$router.push({
        name:"permission",
        query:{
          roleId:row.id,
          roleName:row.roleName
        }
      })
    },
    async confirm() {
      if (!validateAlls(this.$refs.addForm)){return;}

      let res = await api.saveOrUpdate(this.addData);
      if (res) {
        this.$message({
          type: "success",
          message: this.isAdd ? "新增成功" : "修改成功"
        });
        this.initData();
        this.showPopup = false;
      }
    },
    del(row) {
      this.roleId = row.id;
      this.showDelPopup = true;
    },
    async delHandle() {
      let res = await api.deleteRole({ roleId: this.roleId });
      if (res) {
        this.$message({
          type: "success",
          message: "删除成功"
        });
        this.initData();
      }
      this.showDelPopup = false
    },
    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.initData();
    }
  }
};
</script>
<style lang="less">
.approval-list {
  .nav-list {
    overflow: hidden;
    background-color: #f0f2f5;
    width: 100%;

    .li {
      width: 108px;
      height: 46px;
      background: #ececec;
      border-radius: 6px 6px 0px 0px;
      text-align: center;
      line-height: 46px;
      color: #999;
      font-size: 16px;
      float: left;
      margin-left: 14px;
      cursor: pointer;

      &.active {
        font-size: 18px;
        color: #4f85e6;
        background: #fff;
        border-top: 2px solid #4f85e6;
      }
    }
  }
}

.product-plan-config {
  width: 80%;
}

.product-item {
  display: flex;
  background-color: rgba(242, 242, 242, 1);
  margin-bottom: 10px;
  border-radius: 5px;
  width: 80%;
}

.footerBtn {
  padding: 25px;
  text-align: center;
}

</style>
