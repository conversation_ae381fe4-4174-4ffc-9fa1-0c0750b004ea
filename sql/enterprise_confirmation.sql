-- 企业确权管理相关表结构

-- 1. 企业确权表
CREATE TABLE t_enterprise_confirmation (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    legal_code VARCHAR(50) NOT NULL COMMENT '机构编码',
    credit_code VARCHAR(50) NOT NULL COMMENT '统一信用代码',
    enterprise_name VARCHAR(200) NOT NULL COMMENT '企业名称',
    district_code VARCHAR(20) COMMENT '行政区域编码',
    city VARCHAR(200) COMMENT '所在地',
    staff_scale VARCHAR(50) COMMENT '人员规模',
    annual_income VARCHAR(50) COMMENT '营业收入',
    is_duplicate TINYINT DEFAULT 0 COMMENT '是否重复：0-否，1-是',
    is_processed TINYINT DEFAULT 0 COMMENT '是否处理：0-未处理，1-已处理',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    tenant_id VARCHAR(50) NOT NULL COMMENT '租户ID',
    INDEX idx_credit_code (credit_code),
    INDEX idx_legal_code (legal_code)
) COMMENT='企业确权表';

-- 2. 确权处理记录表
CREATE TABLE t_confirmation_process_record (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    credit_code VARCHAR(50) NOT NULL COMMENT '统一信用代码',
    user_id VARCHAR(50) NOT NULL COMMENT '处理人ID',
    user_name VARCHAR(100) NOT NULL COMMENT '处理人姓名',
    remark TEXT COMMENT '批注',
    process_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '处理时间',
    legal_code VARCHAR(50) NOT NULL COMMENT '机构编码',
    legal_name VARCHAR(200) NOT NULL COMMENT '机构名称',
    tenant_id VARCHAR(50) NOT NULL COMMENT '租户ID',
    INDEX idx_credit_code (credit_code),
    INDEX idx_legal_code (legal_code)
) COMMENT='确权处理记录表';
