package com.kbao.kbcelms.questionnaire.entity;

import com.kbao.kbcelms.questionnaire.enums.QuestionTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 问卷问题实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QuestionnaireQuestion implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问题ID
     */
    private Long id;

    /**
     * 问卷ID
     */
    private Long questionnaireId;

    /**
     * 问题标题
     */
    private String title;

    /**
     * 题型：single-单选，multi-多选，text-简答，rating-评分，matrix-矩阵
     */
    private String type;

    /**
     * 关联评分项
     */
    private String scoreId;

    /**
     * 最高分
     */
    private Integer maxScore;

    /**
     * 是否必填：0-非必填，1-必填
     */
    private Integer required;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;

    /**
     * 选项列表（非数据库字段）
     */
    private List<QuestionnaireQuestionOption> options;

    /**
     * 获取题型描述
     */
    public String getTypeDesc() {
        QuestionTypeEnum typeEnum = QuestionTypeEnum.getByCode(this.type);
        return typeEnum != null ? typeEnum.getName() : "未知";
    }

    /**
     * 是否为必填题
     */
    public boolean isRequiredQuestion() {
        return Integer.valueOf(1).equals(this.required);
    }
}