package com.kbao.kbcelms.questionnaire.service;

import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.questionnaire.bean.QuestionnaireAnswerBean;
import com.kbao.kbcelms.questionnaire.dao.QuestionnaireAnswerMapper;
import com.kbao.kbcelms.questionnaire.entity.QuestionnaireAnswer;
import com.kbao.kbcelms.riskMatrix.service.RiskMatrixService;
import com.kbao.kbcelms.util.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 问卷答案服务实现类
 */
@Slf4j
@Service
public class QuestionnaireAnswerService extends BaseSQLServiceImpl<QuestionnaireAnswer, Long, QuestionnaireAnswerMapper> {

    @Autowired
    private RiskMatrixService riskMatrixService;

    @Autowired
    private ThreadPoolUtils threadPoolUtils;
    /**
     * 根据问卷ID和企业ID查询答案列表
     */
    public List<QuestionnaireAnswer> getAnswersByQuestionnaireAndEnterprise(Long questionnaireId, Long enterpriseId) {
        try {
            if (questionnaireId == null || enterpriseId == null) {
                throw new IllegalArgumentException("参数不能为空");
            }

            return this.mapper.selectAnswersByQuestionnaireAndEnterprise(questionnaireId, enterpriseId);
        } catch (Exception e) {
            log.error("查询答案列表失败，questionnaireId: {}, enterpriseId: {}", questionnaireId, enterpriseId, e);
            throw new RuntimeException("查询失败");
        }
    }

    /**
     * 根据问卷ID查询所有答案统计
     */
    public List<Map<String, Object>> getAnswerStatistics(Long questionnaireId) {
        try {
            if (questionnaireId == null) {
                throw new IllegalArgumentException("问卷ID不能为空");
            }

            return this.mapper.selectAnswerStatistics(questionnaireId);
        } catch (Exception e) {
            log.error("查询答案统计失败，questionnaireId: {}", questionnaireId, e);
            throw new RuntimeException("查询失败");
        }
    }

    /**
     * 根据企业ID查询答题记录
     */
    public List<Map<String, Object>> getAnswerRecordsByEnterprise(Long enterpriseId) {
        try {
            if (enterpriseId == null) {
                throw new IllegalArgumentException("企业ID不能为空");
            }

            return this.mapper.selectAnswerRecordsByEnterprise(enterpriseId);
        } catch (Exception e) {
            log.error("查询答题记录失败，enterpriseId: {}", enterpriseId, e);
            throw new RuntimeException("查询失败");
        }
    }

    /**
     * 批量插入或更新答案
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchInsertOrUpdate(List<QuestionnaireAnswer> answers) {
        try {
            if (CollectionUtils.isEmpty(answers)) {
                return true;
            }

            int result = this.mapper.batchInsertOrUpdate(answers);
            return result > 0;
        } catch (Exception e) {
            log.error("批量保存答案失败", e);
            throw new RuntimeException("保存失败");
        }
    }

    /**
     * 删除企业的问卷答案
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByQuestionnaireAndEnterprise(Long questionnaireId, Long enterpriseId) {
        try {
            if (questionnaireId == null || enterpriseId == null) {
                throw new IllegalArgumentException("参数不能为空");
            }

            int result = this.mapper.deleteByQuestionnaireAndEnterprise(questionnaireId, enterpriseId);
            return result > 0;
        } catch (Exception e) {
            log.error("删除企业答案失败，questionnaireId: {}, enterpriseId: {}", questionnaireId, enterpriseId, e);
            throw new RuntimeException("删除失败");
        }
    }

    /**
     * 统计问卷答题企业数量
     */
    public Long countAnsweredEnterprises(Long questionnaireId) {
        try {
            if (questionnaireId == null) {
                throw new IllegalArgumentException("问卷ID不能为空");
            }

            return this.mapper.countAnsweredEnterprises(questionnaireId);
        } catch (Exception e) {
            log.error("统计答题企业数量失败，questionnaireId: {}", questionnaireId, e);
            throw new RuntimeException("统计失败");
        }
    }

    /**
     * 提交问卷答案
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean submitQuestionnaireAnswer(QuestionnaireAnswerBean answerBean) {
        try {
            if (answerBean == null || answerBean.getQuestionnaireId() == null || 
                answerBean.getEnterpriseId() == null || CollectionUtils.isEmpty(answerBean.getAnswers())) {
                throw new IllegalArgumentException("参数不能为空");
            }

            // 删除原有答案
            deleteByQuestionnaireAndEnterprise(answerBean.getQuestionnaireId(), answerBean.getEnterpriseId());

            // 保存新答案
            List<QuestionnaireAnswer> answers = answerBean.getAnswers().stream()
                    .map(answer -> {
                        QuestionnaireAnswer entity = new QuestionnaireAnswer();
                        entity.setQuestionnaireId(answerBean.getQuestionnaireId());
                        entity.setQuestionId(answer.getQuestionId());
                        entity.setQuestionTitle(answer.getQuestionTitle());
                        entity.setEnterpriseId(answerBean.getEnterpriseId());
                        entity.setEnterpriseType(answerBean.getEnterpriseType());
                        entity.setScoreId(answer.getScoreId());
                        entity.setOptionId(answer.getOptionId());
                        entity.setAnswerContent(answer.getAnswerContent());
                        entity.setOptionValue(answer.getOptionValue());
                        entity.setScore(answer.getScore());
                        entity.setShareUserId(answerBean.getShareUserId());
                        entity.setSubmitUserId(answerBean.getSubmitUserId());
                        entity.setAnswerTime(LocalDateTime.now());
                        entity.setCreateTime(LocalDateTime.now());
                        entity.setUpdateTime(LocalDateTime.now());
                        entity.setDeleted(0);
                        return entity;
                    })
                    .collect(Collectors.toList());

            boolean flag = batchInsertOrUpdate(answers);
            //保存问卷后触发报告计算
            try {
                riskMatrixService.loadRisMatrixReport(answerBean.getEnterpriseId(), answerBean.getEnterpriseType());
            }catch (Exception e){
                log.error("计算报告失败", e);
            }
            return flag;
        } catch (Exception e) {
            log.error("提交问卷答案失败", e);
            throw new RuntimeException("提交失败");
        }
    }

    /**
     * 获取企业答案（格式化为Map）
     */
    public List<Map<String, Object>> getEnterpriseAnswers(Long questionnaireId, Long enterpriseId) {
        try {
            if (questionnaireId == null || enterpriseId == null) {
                throw new IllegalArgumentException("参数不能为空");
            }

            List<QuestionnaireAnswer> answers = getAnswersByQuestionnaireAndEnterprise(questionnaireId, enterpriseId);

            return answers.stream()
                    .map(answer -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("questionId", answer.getQuestionId());
                        map.put("answerContent", answer.getAnswerContent());
                        map.put("score", answer.getScore());
                        map.put("answerTime", answer.getAnswerTime());
                        return map;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询企业答案失败，questionnaireId: {}, enterpriseId: {}", questionnaireId, enterpriseId, e);
            throw new RuntimeException("查询失败");
        }
    }

    /**
     * 根据评分项ID列表和企业ID查询答案
     */
    public List<QuestionnaireAnswer> getAnswersByScoreIdsAndEnterprise(List<Long> scoreIds, Long enterpriseId, String enterpriseType) {
        try {
            if (CollectionUtils.isEmpty(scoreIds) || enterpriseId == null) {
                log.warn("参数为空，scoreIds: {}, enterpriseId: {}", scoreIds, enterpriseId);
                return new ArrayList<>();
            }

            return this.mapper.selectAnswersByScoreIdsAndEnterprise(scoreIds, enterpriseId, enterpriseType);
        } catch (Exception e) {
            log.error("根据评分项ID查询答案失败，scoreIds: {}, enterpriseId: {}", scoreIds, enterpriseId, e);
            throw new RuntimeException("查询失败");
        }
    }


}
