package com.kbao.kbcelms.onlineproduct.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.industry.entity.Industry;
import com.kbao.kbcelms.industry.service.IndustryService;
import com.kbao.kbcelms.onlineproduct.bean.OnlineProductConfigQuery;
import com.kbao.kbcelms.onlineproduct.dao.OnlineProductConfigMapper;
import com.kbao.kbcelms.onlineproduct.entity.OnlineProductConfig;
import com.kbao.kbcelms.onlineproduct.vo.OnlineProductConfigVO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbao.tool.util.SysLoginUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 线上产品配置服务实现类
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class OnlineProductConfigService extends BaseSQLServiceImpl<OnlineProductConfig, Long, OnlineProductConfigMapper> {

    private static final Logger logger = LoggerFactory.getLogger(OnlineProductConfigService.class);
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Autowired
    private IndustryService industryService;

    /**
     * 分页查询线上产品配置
     * @param request 分页请求
     * @return 分页结果
     */
    public PageInfo<OnlineProductConfigVO> getPage(PageRequest<OnlineProductConfigQuery> request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<OnlineProductConfigVO> list = mapper.selectOnlineProductConfigList(request.getParam());

        // 为每个VO设置状态名称、行业代码列表和险种类型列表
        for (OnlineProductConfigVO vo : list) {
            vo.setEnabledName(vo.getEnabled() ? "启用" : "禁用");
            
            // 解析行业代码JSON字符串
            List<String> industryCodes = parseIndustryCodes(vo);
            vo.setIndustryCode(industryCodes);
            
            // 解析险种类型JSON字符串
            List<String> insuranceTypes = parseInsuranceTypes(vo);
            vo.setInsuranceTypes(insuranceTypes);
        }

        return new PageInfo<>(list);
    }

    /**
     * 根据ID查询线上产品配置详情
     * @param id 配置ID
     * @return 配置详情
     */
    public OnlineProductConfigVO getById(Long id) {
        OnlineProductConfigVO vo = mapper.selectOnlineProductConfigById(id);
        if (vo != null) {
            vo.setEnabledName(vo.getEnabled() ? "启用" : "禁用");
            
            // 解析行业代码JSON字符串
            List<String> industryCodes = parseIndustryCodes(vo);
            vo.setIndustryCode(industryCodes);
            
            // 解析险种类型JSON字符串
            List<String> insuranceTypes = parseInsuranceTypes(vo);
            vo.setInsuranceTypes(insuranceTypes);
        }
        return vo;
    }

    /**
     * 新增线上产品配置
     * @param vo 配置信息
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    public OnlineProductConfigVO add(OnlineProductConfigVO vo) {
        // 检查配置是否重复
        if (checkConfigExists(vo.getIndustryCode(), vo.getProbability(), vo.getImpact(), vo.getLevel(), null)) {
            throw new RuntimeException("该行业的风险配置已存在");
        }

        // 保存主表
        OnlineProductConfig entity = new OnlineProductConfig();
        BeanUtils.copyProperties(vo, entity);
        entity.setEnabled(vo.getEnabled() ? 1 : 0);
        
        // 转换行业代码列表为JSON字符串
        if (vo.getIndustryCode() != null && !vo.getIndustryCode().isEmpty()) {
            try {
                entity.setIndustryCode(objectMapper.writeValueAsString(vo.getIndustryCode()));
                // 根据行业代码生成行业名称
                entity.setIndustryName(generateIndustryName(vo.getIndustryCode()));
            } catch (Exception e) {
                throw new RuntimeException("行业代码列表转换失败", e);
            }
        }
        
        // 转换险种类型列表为JSON字符串
        if (vo.getInsuranceTypes() != null && !vo.getInsuranceTypes().isEmpty()) {
            try {
                entity.setInsuranceTypes(objectMapper.writeValueAsString(vo.getInsuranceTypes()));
            } catch (Exception e) {
                throw new RuntimeException("险种类型列表转换失败", e);
            }
        }
        
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setCreateUser(BscUserUtils.getUserId());
        entity.setUpdateUser(BscUserUtils.getUserId());
        entity.setTenantId(SysLoginUtils.getUser().getTenantId());
        entity.setIsDeleted(0);

        insertSelective(entity);

        return getById(entity.getId());
    }

    /**
     * 更新线上产品配置
     * @param vo 配置信息
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public OnlineProductConfigVO update(OnlineProductConfigVO vo) {
        // 检查配置是否重复
        if (checkConfigExists(vo.getIndustryCode(), vo.getProbability(), vo.getImpact(), vo.getLevel(), vo.getId())) {
            throw new RuntimeException("该行业的风险配置已存在");
        }

        // 更新主表
        OnlineProductConfig entity = new OnlineProductConfig();
        BeanUtils.copyProperties(vo, entity);
        entity.setEnabled(vo.getEnabled() ? 1 : 0);
        
        // 转换行业代码列表为JSON字符串
        if (vo.getIndustryCode() != null && !vo.getIndustryCode().isEmpty()) {
            try {
                entity.setIndustryCode(objectMapper.writeValueAsString(vo.getIndustryCode()));
                // 根据行业代码生成行业名称
                entity.setIndustryName(generateIndustryName(vo.getIndustryCode()));
            } catch (Exception e) {
                throw new RuntimeException("行业代码列表转换失败", e);
            }
        }
        
        // 转换险种类型列表为JSON字符串
        if (vo.getInsuranceTypes() != null && !vo.getInsuranceTypes().isEmpty()) {
            try {
                entity.setInsuranceTypes(objectMapper.writeValueAsString(vo.getInsuranceTypes()));
            } catch (Exception e) {
                throw new RuntimeException("险种类型列表转换失败", e);
            }
        }
        
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUser(BscUserUtils.getUserId());

        updateByPrimaryKeySelective(entity);

        return getById(vo.getId());
    }

    /**
     * 删除线上产品配置
     * @param id 配置ID
     */
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        // 逻辑删除
        OnlineProductConfig entity = new OnlineProductConfig();
        entity.setId(id);
        entity.setIsDeleted(1);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUser(BscUserUtils.getUserId());
        return updateByPrimaryKeySelective(entity);
    }

    /**
     * 检查配置是否存在
     * @param industryCode 行业代码
     * @param probability 风险概率
     * @param impact 风险影响
     * @param level 风险等级
     * @param id 排除的ID
     * @return 是否存在
     */
    public boolean checkConfigExists(List<String> industryCode, String probability, String impact, String level, Long id) {
        return mapper.checkConfigExists(industryCode, probability, impact, level, id) > 0;
    }

    /**
     * 获取行业选项列表
     * @return 行业选项列表
     * @deprecated 建议使用行业管理模块的树形接口 /api/industry/tree
     */
    public List<IndustryOptionVO> getIndustryOptions() {
        List<IndustryOptionVO> options = new ArrayList<>();
        
        // 兼容性保留，建议前端直接调用行业管理模块的接口
        options.add(new IndustryOptionVO("IT", "信息技术"));
        options.add(new IndustryOptionVO("FINANCE", "金融服务"));
        options.add(new IndustryOptionVO("MANUFACTURE", "制造业"));
        options.add(new IndustryOptionVO("TRADE", "贸易"));
        options.add(new IndustryOptionVO("INTERNET", "互联网"));
        options.add(new IndustryOptionVO("EDUCATION", "教育"));
        options.add(new IndustryOptionVO("MEDICAL", "医疗健康"));
        options.add(new IndustryOptionVO("REAL_ESTATE", "房地产"));
        options.add(new IndustryOptionVO("TRANSPORT", "交通运输"));
        options.add(new IndustryOptionVO("ENERGY", "能源化工"));
        
        return options;
    }

    /**
     * 获取险种类型选项列表
     * @return 险种类型选项列表
     */
    public List<InsuranceTypeOptionVO> getInsuranceTypeOptions() {
        List<InsuranceTypeOptionVO> options = new ArrayList<>();
        
        options.add(new InsuranceTypeOptionVO("employer", "雇主责任险"));
        options.add(new InsuranceTypeOptionVO("group", "团体意外险"));
        options.add(new InsuranceTypeOptionVO("overseas", "境外意外险"));
        options.add(new InsuranceTypeOptionVO("health", "健康保险"));
        options.add(new InsuranceTypeOptionVO("pension", "养老保险"));
        options.add(new InsuranceTypeOptionVO("property", "财产保险"));
        
        return options;
    }

    /**
     * 根据行业代码列表生成行业名称
     */
    private String generateIndustryName(List<String> industryCodes) {
        if (industryCodes == null || industryCodes.isEmpty()) {
            return "";
        }
        
        List<String> industryNames = new ArrayList<>();
        for (String code : industryCodes) {
            try {
                Industry industry = industryService.selectByCode(code);
                if (industry != null) {
                    industryNames.add(industry.getName());
                } else {
                    // 如果找不到对应的行业，使用代码作为名称
                    industryNames.add(code);
                    logger.warn("未找到行业代码对应的行业信息: {}", code);
                }
            } catch (Exception e) {
                logger.error("查询行业信息失败，代码: {}", code, e);
                industryNames.add(code);
            }
        }
        
        return String.join("，", industryNames);
    }

    /**
     * 解析行业代码JSON字符串
     */
    private List<String> parseIndustryCodes(OnlineProductConfigVO vo) {
        if (StringUtils.hasText(vo.getIndustryCodeStr())) {
            try {
                // 判断是否为JSON格式
                if (vo.getIndustryCodeStr().startsWith("[")) {
                    return objectMapper.readValue(vo.getIndustryCodeStr(), 
                        new TypeReference<List<String>>() {});
                } else {
                    // 兼容单个行业代码的情况
                    List<String> result = new ArrayList<>();
                    result.add(vo.getIndustryCodeStr());
                    return result;
                }
            } catch (Exception e) {
                logger.warn("解析行业代码JSON失败: {}", vo.getIndustryCodeStr(), e);
                // 降级处理，当作单个代码
                List<String> result = new ArrayList<>();
                result.add(vo.getIndustryCodeStr());
                return result;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 解析险种类型JSON字符串
     */
    private List<String> parseInsuranceTypes(OnlineProductConfigVO vo) {
        if (StringUtils.hasText(vo.getInsuranceTypesStr())) {
            try {
                return objectMapper.readValue(vo.getInsuranceTypesStr(), 
                    new TypeReference<List<String>>() {});
            } catch (Exception e) {
                logger.warn("解析险种类型JSON失败: {}", vo.getInsuranceTypesStr(), e);
                return new ArrayList<>();
            }
        }
        return new ArrayList<>();
    }

    /**
     * 行业选项VO
     */
    public static class IndustryOptionVO {
        private String dicItemCode;
        private String dicItemName;

        public IndustryOptionVO() {}

        public IndustryOptionVO(String dicItemCode, String dicItemName) {
            this.dicItemCode = dicItemCode;
            this.dicItemName = dicItemName;
        }

        public String getDicItemCode() {
            return dicItemCode;
        }

        public void setDicItemCode(String dicItemCode) {
            this.dicItemCode = dicItemCode;
        }

        public String getDicItemName() {
            return dicItemName;
        }

        public void setDicItemName(String dicItemName) {
            this.dicItemName = dicItemName;
        }
    }

    /**
     * 险种类型选项VO
     */
    public static class InsuranceTypeOptionVO {
        private String value;
        private String label;

        public InsuranceTypeOptionVO() {}

        public InsuranceTypeOptionVO(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }
    }
}
