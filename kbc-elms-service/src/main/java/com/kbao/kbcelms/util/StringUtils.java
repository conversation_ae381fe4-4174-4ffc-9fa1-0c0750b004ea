package com.kbao.kbcelms.util;

public class StringUtils {

    public static String getMaxStr(String m, String n) {
        if (m == null && n == null) return null;
        if (m == null) return n;
        if (n == null) return m;

        return m.compareTo(n) >= 0 ? m : n;
    }

    /**
     * 通用的下划线转驼峰方法
     */
    public static String underscoreToCamelCase(String underscore) {
        if (underscore == null || underscore.isEmpty()) {
            return underscore;
        }
        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;
        for (int i = 0; i < underscore.length(); i++) {
            char c = underscore.charAt(i);
            if (c == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    result.append(Character.toUpperCase(c));
                    nextUpperCase = false;
                } else {
                    result.append(Character.toLowerCase(c));
                }
            }
        }
        return result.toString();
    }
}
