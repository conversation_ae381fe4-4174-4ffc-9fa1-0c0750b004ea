<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
      @export-list="handleExportFields"
      @import-list="handleImportFields"
    />

    <!-- 隐藏的文件上传组件 -->
    <input
      ref="fileInput"
      type="file"
      accept=".xlsx,.xls"
      style="display: none"
      @change="handleFileChange"
    />
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { getFormConfigDetail, addFormConfig, updateFormConfig, exportFormConfigFields, importFormConfigFields } from '@/api/basicConfig'
import {rootPath} from "@/utils/globalParam";
import Axios from "axios";
import store from "@/store";
import fileDownload from "js-file-download";

export default {
  name: 'FormConfigEdit',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      // 分类枚举
      typeEnum: {
        '1': { label: '企业信息' },
        '2': { label: '员福配置' },
        '3': { label: '企业补充信息' }
      },
      downloadURL: "/api/formConfig/fields/export",
      isEdit: false,
      isView: false,
      loading: false,
      form: {
        id: this.$route.params.id || null,
        configName: '',
        configCode: '',
        type: '1',
        status: '1',
        remark: '',
        fields: []
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              { prop: 'configName', label: '配置名称', type: 'input', placeholder: '请输入配置名称', maxlength: 100, showWordLimit: true, required: true },
              { prop: 'configCode', label: '配置编码', type: 'input', placeholder: '请输入配置编码', maxlength: 50, required: true }
            ],
            [
              {
                prop: 'type',
                label: '分类',
                type: 'select',
                placeholder: '请选择分类',
                required: true,
                options: [
                  { label: '企业信息', value: '1' },
                  { label: '员福配置', value: '2' },
                  { label: '企业补充信息', value: '3' }
                ]
              },
              { prop: 'status', label: '状态', type: 'radio', options: [ { label: '启用', value: "1" }, { label: '禁用', value: "0" } ] }
            ],
            [
              { prop: 'remark', label: '描述', type: 'textarea', maxlength: 200, rows: 3, showWordLimit: true, span: 24 }
            ]
          ]
        },
        {
          title: '字段配置',
          icon: 'el-icon-s-operation',
          fields: [
            [
              {
                type: 'list',
                prop: 'fields',
                label: '字段列表',
                icon: 'el-icon-s-operation',
                addText: '添加字段',
                showImportExport: true, // 显示导入导出按钮
                min: 0,
                wrapActions: true,
                defaultRow: () => {
                  // 计算下一个序号
                  const maxSort = this.form.fields && this.form.fields.length > 0
                    ? Math.max(...this.form.fields.map(f => f.sort || 0))
                    : 0
                  return {
                    fieldName: '',
                    fieldCode: '',
                    showType: '1',
                    required: '0',
                    change: '1',
                    defaultValue: '',
                    validation: '',
                    additional: '',
                    additionalPlaceholder: '请输入单位',
                    remark: '',
                    sort: maxSort + 1
                  }
                },
                columns: [
                  { prop: 'sort', label: '序号', type: 'number', required: true, width: 90, min: 1 },
                  { prop: 'fieldName', label: '字段名称', type: 'input', required: true, width: 150 },
                  { prop: 'fieldCode', label: '字段编码', type: 'input', required: true, width: 150 },
                  {
                    prop: 'showType',
                    label: '展示类型',
                    type: 'select',
                    options: [
                      { label: '输入框', value: '1' },
                      { label: '单选框', value: '2' },
                      { label: '下拉框', value: '3' },
                      { label: '日期选择', value: '4' },
                      { label: '日期范围', value: '5' },
                      { label: '文本域', value: '6' }
                    ],
                    width: 130,
                    required: true,
                    onChange: (val, row) => {
                      if (val === '1') {
                        row.additionalPlaceholder = '请输入单位';
                      } else if (val === '2' || val === '3') {
                        row.additionalPlaceholder = '请输入选项，格式：code-value，逗号分隔，如：1-是,0-否';
                      } else if (val === '5') {
                        row.additionalPlaceholder = '请输入日期范围，格式：开始日期,结束日期';
                      } else {
                        row.additionalPlaceholder = '不需要输入';
                      }
                    }
                  },
                  {
                    prop: 'required',
                    label: '是否必填',
                    type: 'select',
                    options: [
                      { label: '是', value: '1' },
                      { label: '否', value: '0' }
                    ],
                    width: 90
                  },
                  {
                    prop: 'change',
                    label: '是否可修改',
                    type: 'select',
                    options: [
                      { label: '是', value: '1' },
                      { label: '否', value: '0' }
                    ],
                    width: 90
                  },
                  {
                    prop: 'defaultValue',
                    label: '默认值',
                    type: 'input',
                    width: 100
                  },
                  {
                    prop: 'validation',
                    label: '校验规则',
                    type: 'input',
                    width: 150
                  },
                  {
                    prop: 'additional',
                    label: '附加属性',
                    type: 'input',
                    width: 250,
                    placeholder: row => row.additionalPlaceholder || '请输入单位'
                  },
                  { prop: 'remark', label: '描述', type: 'input', width: 150 }
                ],
                actions: [
                  {
                    label: '删除',
                    icon: 'el-icon-delete',
                    type: 'danger',
                    show: (row, index, isView) => !isView,
                    onClick: (row, index, formData) => {
                      formData.fields.splice(index, 1)
                    }
                  }
                ]
              }
            ]
          ]
        }
      ],
      formRules: {
        configName: [{ required: true, message: '请输入配置名称', trigger: 'blur' }],
        configCode: [{ required: true, message: '请输入配置编码', trigger: 'blur' }],
        type: [{ required: true, message: '请选择分类', trigger: 'change' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
      }
    }
  },
  computed: {
    pageTitle() {
      return this.isView ? '查看表单配置' : (this.isEdit ? '编辑表单配置' : '新增表单配置')
    },
    pageIcon() {
      return this.isView ? 'el-icon-view' : (this.isEdit ? 'el-icon-edit' : 'el-icon-plus')
    },
    breadcrumbItems() {
      return [
        { text: '表单配置', icon: 'el-icon-document-copy', to: { name: 'formConfig' } },
        { text: this.pageTitle, icon: this.pageIcon }
      ]
    }
  },
  created() {
    const id = this.$route.params.id
    const mode = this.$route.query.mode
    if (id) {
      this.isEdit = mode !== 'view'
      this.isView = mode === 'view'
      this.loadData(id)
    }
  },
  methods: {
    async loadData(id) {
      this.loading = true
      try {
        const response = await getFormConfigDetail(id)
        if (response) {
          // 处理字段的additional属性回显
          if (Array.isArray(response.fields)) {
            response.fields = response.fields.map(f => {
              if (f.showType === '1' && f.additional && f.additional.inputUnit) {
                f.additional = f.additional.inputUnit
              } else if ((f.showType === '2' || f.showType === '3') && f.additional && Array.isArray(f.additional.selectOptions)) {
                // 将对象数组转换为"code-value"格式
                if (f.additional.selectOptions.length > 0 && typeof f.additional.selectOptions[0] === 'object') {
                  f.additional = f.additional.selectOptions.map(option => `${option.code}-${option.value}`).join(',')
                } else {
                  // 兼容旧格式（纯字符串数组）
                  f.additional = f.additional.selectOptions.join(',')
                }
              } else if (f.showType === '5' && f.additional && f.additional.dateRangeField) {
                // 将日期范围对象转换为"start,end"格式
                f.additional = `${f.additional.dateRangeField.start || ''},${f.additional.dateRangeField.end || ''}`
              } else {
                f.additional = f.additional || ''
              }
              // 确保有序号，如果没有则设置为1
              if (!f.sort) {
                f.sort = 1
              }
              // 设置additionalPlaceholder
              if (f.showType === '1') {
                f.additionalPlaceholder = '请输入单位'
              } else if (f.showType === '2' || f.showType === '3') {
                f.additionalPlaceholder = '请输入选项，格式：code-value，逗号分隔，如：1-是,0-否'
              } else if (f.showType === '5') {
                f.additionalPlaceholder = '请输入日期范围，格式：开始日期,结束日期'
              } else {
                f.additionalPlaceholder = '不需要输入'
              }
              return f
            })
            // 按序号排序
            response.fields.sort((a, b) => (a.sort || 0) - (b.sort || 0))
          }
          this.form = { ...response }
        } else {
          this.$message.error(response.message || '加载数据失败')
        }
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    async handleSave() {
      try {
        await this.$refs.universalForm.validate()
        // 校验 fieldCode 不能重复
        if (Array.isArray(this.form.fields)) {
          const codes = this.form.fields.map(f => f.fieldCode && f.fieldCode.trim()).filter(Boolean)
          const codeSet = new Set(codes)
          if (codes.length !== codeSet.size) {
            this.$message.error('字段编码（fieldCode）不能重复！')
            return
          }
        }
        this.loading = true
        // 保存前处理additional，使用新变量，避免污染页面数据
        let fields = []
        if (Array.isArray(this.form.fields)) {
          fields = this.form.fields.map(f => {
            const newField = { ...f }
            if (f.additional) {
              if (f.showType === '1') {
                newField.additional = { inputUnit: f.additional || '' }
              } else if (f.showType === '2' || f.showType === '3' ) {
                // 处理"code-value"格式，转换为对象数组
                const options = (f.additional || '').split(',').map(s => s.trim()).filter(Boolean)
                newField.additional = {
                  selectOptions: options.map(option => {
                    if (option.includes('-')) {
                      const [code, value] = option.split('-', 2)
                      return { code: code.trim(), value: value.trim() }
                    } else {
                      // 兼容旧格式（纯字符串）
                      return option
                    }
                  })
                }
              } else if (f.showType === '5') {
                // 处理日期范围格式，转换为dateRangeField对象
                const dateRangeField = (f.additional || '').split(',').map(s => s.trim())
                newField.additional = {
                  dateRangeField: {
                    start: dateRangeField[0] || '',
                    end: dateRangeField[1] || ''
                  }
                }
              } else {
                newField.additional = {}
              }
            } else {
              newField.additional = {}
            }
            return newField
          })
        }
        // 构造新form对象用于保存
        const saveForm = { ...this.form, fields }
        let response
        if (this.isEdit) {
          response = await updateFormConfig(saveForm)
        } else {
          response = await addFormConfig(saveForm)
        }
        if (response) {
          this.$message.success(this.isEdit ? '更新成功' : '创建成功')
          this.$router.back()
        } else {
          this.$message.error(response.message || '保存失败')
        }
      } catch (error) {
        this.$message.error('保存失败')
        console.error('保存失败:', error)
      } finally {
        this.loading = false
      }
    },
    handleBack() {
      this.$router.back()
    },
    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push({ name: item.to.name })
      }
    },
    // 导出字段列表
    async handleExportFields() {
          let fileurl = rootPath + this.downloadURL;
          const loadingInstance = this.$loading({
            fullscreen: true,
            lock: true,
            text: "加载中...",
            target: document.getElementsByTagName("body")[0]
          });
          Axios({
            method: "post",
            url: fileurl,
            headers: {
              access_token: sessionStorage.getItem("LoginAccessToken"),
              tenantId:
                store.state.layoutStore.currentLoginUser.tenantId ||
                sessionStorage.getItem("tenantId"),
              funcId:
                store.state.layoutStore.currentLoginUser.funcId ||
                sessionStorage.getItem("funcId")
            },
            data: {id: this.form.id},
            responseType: "blob"
          }).then(res => {
            if (res.status === 200) {
              let file_name = `表单字段列表_${this.form.configName || ''}_${new Date().toISOString().slice(0, 10)}.xlsx`
              fileDownload(res.data, decodeURI(res.headers["file-name"] || file_name));
              setTimeout(function() {
                loadingInstance.close();
              }, 1000);
            } else {
              loadingInstance.close();
            }
          }).catch(() => {
            loadingInstance.close();
            this.$message.error("导出失败");
          });
        },

    // 导入字段
    handleImportFields(field) {
      if (!this.form.id) {
        this.$message.warning('请先保存配置后再导入字段')
        return
      }
      this.$refs.fileInput.click()
    },

    // 处理文件选择
    async handleFileChange(event) {
      const file = event.target.files[0]
      if (!file) return

      try {
        this.loading = true
        const response = await importFormConfigFields(this.form.id, file)

        if (response && Array.isArray(response)) {
          // 处理导入的字段数据
          const importedFields = response.map((field, index) => {
            // 处理additional字段的回显
            if (field.showType === '1' && field.additional && field.additional.inputUnit) {
              field.additional = field.additional.inputUnit
            } else if ((field.showType === '2' || field.showType === '3') && field.additional && Array.isArray(field.additional.selectOptions)) {
              // 将对象数组转换为"code-value"格式
              if (field.additional.selectOptions.length > 0 && typeof field.additional.selectOptions[0] === 'object') {
                field.additional = field.additional.selectOptions.map(option => `${option.code}-${option.value}`).join(',')
              } else {
                // 兼容旧格式（纯字符串数组）
                field.additional = field.additional.selectOptions.join(',')
              }
            } else if (field.showType === '5' && field.additional && field.additional.dateRangeField) {
              // 将日期范围对象转换为"start,end"格式
              field.additional = `${field.additional.dateRangeField.start || ''},${field.additional.dateRangeField.end || ''}`
            } else {
              field.additional = field.additional || ''
            }
            // 如果没有序号或序号为0，则设置默认序号
            if (!field.sort || field.sort === 0) {
              field.sort = index + 1
            }
            field.additionalPlaceholder = field.showType === '1' ? '请输入单位' :
                                               (field.showType === '2' || field.showType === '3') ? '请输入选项，格式：code-value，逗号分隔，如：1-是,0-否' :
                                               field.showType === '5' ? '请输入日期范围，格式：开始日期,结束日期' : '不需要输入'
            return field
          })

          // 更新表单数据
          this.form.fields = importedFields
          this.$message.success(`成功导入 ${importedFields.length} 个字段`)
        } else {
          this.$message.error('导入数据格式错误')
        }
      } catch (error) {
        this.$message.error('导入失败：' + (error.message || '未知错误'))
        console.error('导入失败:', error)
      } finally {
        this.loading = false
        // 清空文件输入框
        event.target.value = ''
      }
    }
  }
}
</script>
