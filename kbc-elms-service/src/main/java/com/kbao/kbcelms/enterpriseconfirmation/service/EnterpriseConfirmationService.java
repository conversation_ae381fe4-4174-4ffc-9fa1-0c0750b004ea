package com.kbao.kbcelms.enterpriseconfirmation.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcelms.enterpriseconfirmation.bean.*;import com.kbao.kbcelms.enterpriseconfirmation.entity.EnterpriseConfirmation;
import com.kbao.kbcelms.enterpriseconfirmation.entity.ConfirmationProcessRecord;
import com.kbao.kbcelms.enterpriseconfirmation.dao.EnterpriseConfirmationMapper;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.kbcelms.enterprise.base.service.EnterpriseBasicInfoService;
import com.kbao.kbcelms.formConfig.service.FormConfigService;import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import com.kbao.kbcelms.opportunity.service.OpportunityService;import com.kbao.kbcelms.usertenant.entity.UserTenant;import com.kbao.kbcelms.usertenant.service.UserTenantService;import com.kbao.kbcelms.util.StringUtils;import com.kbao.kbcucs.agent.model.AgentFullInfoVO;import com.kbao.kbcucs.client.AgentWebClientService;import com.kbao.kbcucs.client.nonuser.AgentWebV2ClientService;import com.kbao.kbcucs.context.RequestContext;
import com.kbao.kbcucs.user.model.UserInfoResp;
import com.kbao.tool.util.EmptyUtils;import com.kbao.tool.util.SysLoginUtils;import org.springframework.beans.BeanUtils;import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.function.Function;import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 企业确权Service
 * @Date 2025-08-20
 */
@Service
public class EnterpriseConfirmationService extends BaseSQLServiceImpl<EnterpriseConfirmation, Integer, EnterpriseConfirmationMapper> {

    @Autowired
    private EnterpriseBasicInfoService enterpriseBasicInfoService;
    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;
    @Autowired
    private ConfirmationProcessRecordService confirmationProcessRecordService;
    @Autowired
    private UserTenantService userTenantService;
    @Autowired
    private AgentWebClientService agentWebClientService;
    @Autowired
    private OpportunityService opportunityService;
    @Autowired
    private FormConfigService formConfigService;

    /**
     * 分页查询企业确权列表（仅查询重复记录）
     */
    public PageInfo<EnterpriseConfirmationListVo> getConfirmationList(PageRequest<EnterpriseConfirmationSearchVo> pageRequest) {
        // TODO: 获取当前登录用户所在机构信息，后续补充
        String userId = ElmsContext.getUser().getUserId();
        String legalCode = userTenantService.getLegalCodeByUserId(userId);
        if (legalCode == null) {
            return new PageInfo<>();
        }
        EnterpriseConfirmationSearchVo param = pageRequest.getParam();
        param.setLegalCode(legalCode); // 只查询当前机构的记录

        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<EnterpriseConfirmationListVo> list = mapper.getConfirmationList(param);
        return new PageInfo<>(list);
    }

    /**
     * 获取企业确权详情
     */
    public EnterpriseConfirmationDetailVo getConfirmationDetail(Integer id) {
        EnterpriseConfirmation confirmation = super.selectByPrimaryKey(id);
        if (confirmation == null) {
            throw new BusinessException("企业确权记录不存在");
        }
        String userId = ElmsContext.getUser().getUserId();
        String legalCode = userTenantService.getLegalCodeByUserId(userId);
        EnterpriseConfirmationDetailVo result = new EnterpriseConfirmationDetailVo();

        // 获取企业基本信息
        EnterpriseBasicInfo basicInfo = enterpriseBasicInfoService.findByCreditCode(confirmation.getCreditCode());
        EnterpriseBaseInfoVo baseInfoVo = new EnterpriseBaseInfoVo();
        baseInfoVo.setName(basicInfo.getName());
        baseInfoVo.setCreditCode(basicInfo.getCreditCode());
        baseInfoVo.setAnnualIncome(confirmation.getAnnualIncome());
        baseInfoVo.setScale(basicInfo.getScale());
        baseInfoVo.setStaffScale(confirmation.getStaffScale());
        result.setEnterpriseBasicInfo(baseInfoVo);

        // 获取创建企业时的录入信息
        List<GenAgentEnterprise> agentEnterpriseList = genAgentEnterpriseService.getByCreditCode(confirmation.getCreditCode());
        GenAgentEnterprise enterprise = new GenAgentEnterprise();
        for (int i = 0; i < agentEnterpriseList.size(); i++) {
            GenAgentEnterprise ae = agentEnterpriseList.get(i);
            if (legalCode.equals(ae.getLegalCode())) {
                enterprise = ae;
                agentEnterpriseList.remove(i);
                break;
            }
        }
        AgentFullInfoVO agentInfo = this.getUserByAgentCode(enterprise.getAgentCode());
        AgentEnterpriseMainVo mainVo = new AgentEnterpriseMainVo();
        BeanUtils.copyProperties(enterprise, mainVo);
        if (agentInfo != null) {
            mainVo.setCreator(agentInfo.getAgentName());
            mainVo.setLegalName(agentInfo.getLegalName());
            mainVo.setSalesCenterName(agentInfo.getSalesCenterName());
        }
        result.setGenAgentEnterprise(mainVo);

        Map<Integer,String> insureTypeMap = formConfigService.getInsureTypeMap();
        // 获取重复企业创建人信息列表
        List<AgentEnterpriseOtherVo> otherList = new ArrayList<>();
        if (EmptyUtils.isNotEmpty(agentEnterpriseList)) {
            Map<Integer,GenAgentEnterprise> agentEnterpriseMap = agentEnterpriseList.stream().collect(Collectors.toMap(GenAgentEnterprise::getId, Function.identity(), (v1, v2) -> v1));
            List<Integer> agentEnterpriseIds = new ArrayList<>(agentEnterpriseMap.keySet());
            List<AgentEnterpriseOtherDto> agentEnterpriseOtherList = opportunityService.getOpportunityNum(agentEnterpriseIds);
            for (AgentEnterpriseOtherDto dto : agentEnterpriseOtherList) {
                AgentEnterpriseOtherVo otherVo = new AgentEnterpriseOtherVo();
                GenAgentEnterprise agentEnterprise = agentEnterpriseMap.get(dto.getAgentEnterpriseId());
                AgentFullInfoVO otherAgentInfo = this.getUserByAgentCode(enterprise.getAgentCode());
                if (otherAgentInfo != null) {
                    otherVo.setCreator(otherAgentInfo.getAgentName());
                    otherVo.setLegalName(otherAgentInfo.getLegalName());
                    otherVo.setSalesCenterName(otherAgentInfo.getSalesCenterName());
                }
                otherVo.setCreateTime(agentEnterprise.getCreateTime());
                otherVo.setOpportunityNum(dto.getOpportunityNum());
                String underwayEmployeeInsureTypes = this.getInsureTypeNames(insureTypeMap, dto.getUnderwayEmployeeInsureTypes());
                otherVo.setUnderwayInsureTypes(underwayEmployeeInsureTypes + dto.getUnderwayGeneralInsureTypes());
                String lockEmployeeInsureTypes = this.getInsureTypeNames(insureTypeMap, dto.getLockEmployeeInsureTypes());
                otherVo.setLockInsureTypes(lockEmployeeInsureTypes + dto.getLockGeneralInsureTypes());
                String issuedEmployeeInsureTypes = this.getInsureTypeNames(insureTypeMap, dto.getIssuedEmployeeInsureTypes());
                otherVo.setIssuedInsureTypes(issuedEmployeeInsureTypes + dto.getIssuedGeneralInsureTypes());
                otherList.add(otherVo);
            }
        }
        result.setDuplicateCreators(otherList);

        // 获取处理记录
        List<ConfirmationProcessRecord> processRecords = confirmationProcessRecordService.getByCreditCode(confirmation.getCreditCode());
        result.setProcessRecords(processRecords);

        return result;
    }

    private AgentFullInfoVO getUserByAgentCode(String agentCode) {
        Result<AgentFullInfoVO> agentFullInfo = agentWebClientService.getAgentFullInfo(agentCode);
        if (agentFullInfo == null) {
            return null;
        }
        return agentFullInfo.getDatas();
    }

    private String getInsureTypeNames(Map<Integer,String> insureTypeMap, String insureTypeIds) {
        if (EmptyUtils.isEmpty(insureTypeMap) || EmptyUtils.isEmpty(insureTypeIds)) {
            return "";
        }
        String[] insureTypeIdArr = insureTypeIds.split(",");
        StringBuilder insureTypeName = new StringBuilder();
        for (String s : insureTypeIdArr) {
            String name = insureTypeMap.get(Integer.parseInt(s));
            if (EmptyUtils.isEmpty(name)) {
                continue;
            }
            insureTypeName.append(insureTypeMap.get(Integer.parseInt(s))).append(",");
        }
        if (insureTypeName.length() == 0) {
            return "";
        }
        return insureTypeName.substring(0, insureTypeName.length() - 1);
    }

    /**
     * 插入企业确权记录并检查重复
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertConfirmationRecord(EnterpriseBasicInfo basicInfo) {
        UserInfoResp userInfo = RequestContext.UserInfoResp.get();

        // 创建确权记录
        EnterpriseConfirmation confirmation = new EnterpriseConfirmation();
        confirmation.setLegalCode(userInfo.getLegalCode());
        confirmation.setCreditCode(basicInfo.getCreditCode());
        confirmation.setEnterpriseName(basicInfo.getName());
        confirmation.setDistrictCode(basicInfo.getDistrictCode());
        confirmation.setCity(basicInfo.getCity());
        confirmation.setStaffScale(basicInfo.getStaffNumRange());
        confirmation.setAnnualIncome(StringUtils.formatAmount(basicInfo.getLatestBusinessIncome()));
        confirmation.setTenantId(userInfo.getTenantId());
        confirmation.setCreateTime(new Date());

        // 检查是否有相同统一信用代码的记录
        DuplicateInfoVo duplicateInfo = mapper.getDuplicateNum(userInfo.getLegalCode(), basicInfo.getCreditCode());
        if (duplicateInfo.getDuplicateNum() > 0) {
            mapper.batchUpdateDuplicateFlag(basicInfo.getCreditCode(), 1, 0);
        }
        if (duplicateInfo.getDuplicateLegalNum() == 0) {
            confirmation.setIsDuplicate(0);
            confirmation.setIsProcessed(1);
            mapper.insert(confirmation);
        }
    }
}
