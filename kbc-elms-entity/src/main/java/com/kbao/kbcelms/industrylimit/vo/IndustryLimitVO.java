package com.kbao.kbcelms.industrylimit.vo;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 行业限制规则视图对象
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@ApiModel(value = "IndustryLimitVO", description = "行业限制规则视图对象")
public class IndustryLimitVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String name;

    /**
     * 规则编码
     */
    @ApiModelProperty(value = "规则编码")
    private String code;

    /**
     * 规则描述
     */
    @ApiModelProperty(value = "规则描述")
    private String description;

    /**
     * 状态(0-禁用,1-启用)
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称")
    private String statusName;

    /**
     * 条件列表
     */
    @ApiModelProperty(value = "条件列表")
    private List<IndustryLimitConditionVO> conditions;

    /**
     * 执行动作列表
     */
    @ApiModelProperty(value = "执行动作列表")
    private List<IndustryLimitActionVO> actionList;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;
}
