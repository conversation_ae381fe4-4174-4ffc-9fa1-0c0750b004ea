<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.questionnaire.dao.QuestionnaireMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.questionnaire.entity.Questionnaire">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="LONGVARCHAR"/>
        <result column="enterprise_types" property="enterpriseTypes" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- VO结果映射 -->
    <resultMap id="VOResultMap" type="com.kbao.kbcelms.questionnaire.vo.QuestionnaireVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="LONGVARCHAR"/>
        <result column="enterprise_types" property="enterpriseTypes" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="status_desc" property="statusDesc" jdbcType="VARCHAR"/>
        <result column="question_count" property="questionCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 详情结果映射（包含问题列表和选项） -->
    <resultMap id="DetailResultMap" type="com.kbao.kbcelms.questionnaire.vo.QuestionnaireVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="LONGVARCHAR"/>
        <result column="enterprise_types" property="enterpriseTypes" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="status_desc" property="statusDesc" jdbcType="VARCHAR"/>
        <result column="question_count" property="questionCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <collection property="questions" ofType="com.kbao.kbcelms.questionnaire.vo.QuestionnaireQuestionVO">
            <id column="q_id" property="id" jdbcType="BIGINT"/>
            <result column="q_questionnaire_id" property="questionnaireId" jdbcType="BIGINT"/>
            <result column="q_title" property="title" jdbcType="VARCHAR"/>
            <result column="q_type" property="type" jdbcType="VARCHAR"/>
            <result column="q_type_desc" property="typeDesc" jdbcType="VARCHAR"/>
            <result column="q_score_id" property="scoreId" jdbcType="BIGINT"/>
            <result column="q_max_score" property="maxScore" jdbcType="INTEGER"/>
            <result column="q_required" property="required" jdbcType="TINYINT"/>
            <result column="q_sort_order" property="sortOrder" jdbcType="INTEGER"/>
            <result column="q_option_count" property="optionCount" jdbcType="INTEGER"/>
        </collection>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, title, description, enterprise_types, status, create_time, update_time, 
        create_user, update_user, deleted
    </sql>

    <!-- VO字段列表 -->
    <sql id="VO_Column_List">
        q.id, q.title, q.description, q.enterprise_types, q.status,
        CASE q.status 
            WHEN 0 THEN '禁用' 
            WHEN 1 THEN '启用' 
            WHEN 2 THEN '草稿' 
            ELSE '未知' 
        END as status_desc,
        (SELECT COUNT(*) FROM t_questionnaire_question qq WHERE qq.questionnaire_id = q.id AND qq.deleted = 0) as question_count,
        q.create_time, q.update_time, q.create_user, q.update_user
    </sql>

    <!-- 分页查询问卷列表 -->
    <select id="selectQuestionnairePage" resultMap="VOResultMap">
        SELECT <include refid="VO_Column_List"/>
        FROM t_questionnaire q
        WHERE q.deleted = 0
        <if test="query != null">
            <if test="query.title != null and query.title != ''">
                AND q.title LIKE CONCAT('%', #{query.title}, '%')
            </if>
            <if test="query.status != null">
                AND q.status = #{query.status}
            </if>
            <if test="query.enterpriseType != null and query.enterpriseType != ''">
                AND (FIND_IN_SET(#{query.enterpriseType}, q.enterprise_types) > 0 OR q.enterprise_types IS NULL OR q.enterprise_types = '')
            </if>
            <if test="query.createUser != null and query.createUser != ''">
                AND q.create_user = #{query.createUser}
            </if>
        </if>
        ORDER BY q.update_time DESC
    </select>

    <!-- 根据ID查询问卷详情（包含问题和选项数量） -->
    <select id="selectQuestionnaire" resultMap="DetailResultMap">
        SELECT
            q.id, q.title, q.description, q.enterprise_types, q.status,
            CASE q.status
                WHEN 0 THEN '禁用'
                WHEN 1 THEN '启用'
                WHEN 2 THEN '草稿'
                ELSE '未知'
            END as status_desc,
            (SELECT COUNT(*) FROM t_questionnaire_question qq WHERE qq.questionnaire_id = q.id AND qq.deleted = 0) as question_count,
            q.create_time, q.update_time, q.create_user, q.update_user,
            qq.id as q_id, qq.questionnaire_id as q_questionnaire_id, qq.title as q_title,
            qq.type as q_type,
            CASE qq.type
                WHEN 'single' THEN '单选题'
                WHEN 'multi' THEN '多选题'
                WHEN 'text' THEN '简答题'
                WHEN 'rating' THEN '评分题'
                WHEN 'matrix' THEN '矩阵题'
                ELSE '未知'
            END as q_type_desc,
            qq.score_id as q_score_id, qq.max_score as q_max_score,
            qq.required as q_required, qq.sort_order as q_sort_order,
            COALESCE(oc.option_count, 0) as q_option_count
        FROM t_questionnaire q
        LEFT JOIN t_questionnaire_question qq ON q.id = qq.questionnaire_id AND qq.deleted = 0
        LEFT JOIN (
            SELECT question_id, COUNT(*) as option_count
            FROM t_questionnaire_question_option
            WHERE deleted = 0
            GROUP BY question_id
        ) oc ON qq.id = oc.question_id
        WHERE q.deleted = 0
        <if test="id != null and id != ''">
            AND q.id = #{id}
        </if>
        <if test="enterpriseType != null and enterpriseType != ''">
            AND (FIND_IN_SET(#{query.enterpriseType}, q.enterprise_types) > 0 OR q.enterprise_types IS NULL OR q.enterprise_types = '')
        </if>
        ORDER BY qq.sort_order ASC
    </select>

    <!-- 查询问卷基本信息 -->
    <select id="selectQuestionnaireBase" resultMap="DetailResultMap">
        SELECT <include refid="VO_Column_List"/>
        FROM t_questionnaire q
        WHERE q.deleted = 0
        <if test="id != null and id != ''">
            AND q.id = #{id}
        </if>
        <if test="enterpriseType != null and enterpriseType != ''">
            AND (FIND_IN_SET(#{enterpriseType}, q.enterprise_types) > 0 OR q.enterprise_types IS NULL OR q.enterprise_types = '')
        </if>
        ORDER BY q.update_time DESC
    </select>



    <!-- 统计问卷数量 -->
    <select id="countQuestionnaires" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_questionnaire q
        WHERE q.deleted = 0
        <if test="query != null">
            <if test="query.title != null and query.title != ''">
                AND q.title LIKE CONCAT('%', #{query.title}, '%')
            </if>
            <if test="query.status != null">
                AND q.status = #{query.status}
            </if>
            <if test="query.enterpriseType != null and query.enterpriseType != ''">
                AND (FIND_IN_SET(#{query.enterpriseType}, q.enterprise_types) > 0 OR q.enterprise_types IS NULL OR q.enterprise_types = '')
            </if>
            <if test="query.createUser != null and query.createUser != ''">
                AND q.create_user = #{query.createUser}
            </if>
        </if>
    </select>

    <!-- 基础CRUD操作 -->

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_questionnaire
        WHERE id = #{id} AND deleted = 0
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.kbao.kbcelms.questionnaire.entity.Questionnaire" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_questionnaire (
            title, description, enterprise_types, status, create_time, update_time,
            create_user, update_user, deleted
        ) VALUES (
            #{title}, #{description}, #{enterpriseTypes}, #{status}, #{createTime}, #{updateTime},
            #{createUser}, #{updateUser}, #{deleted}
        )
    </insert>

    <!-- 选择性插入 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.questionnaire.entity.Questionnaire" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_questionnaire
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="description != null">description,</if>
            <if test="enterpriseTypes != null">enterprise_types,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="deleted != null">deleted,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="description != null">#{description},</if>
            <if test="enterpriseTypes != null">#{enterpriseTypes},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="deleted != null">#{deleted},</if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.questionnaire.entity.Questionnaire">
        UPDATE t_questionnaire SET
            title = #{title},
            description = #{description},
            enterprise_types = #{enterpriseTypes},
            status = #{status},
            update_time = #{updateTime},
            update_user = #{updateUser}
        WHERE id = #{id}
    </update>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.questionnaire.entity.Questionnaire">
        UPDATE t_questionnaire
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="enterpriseTypes != null">enterprise_types = #{enterpriseTypes},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据主键删除（逻辑删除） -->
    <update id="deleteByPrimaryKey">
        UPDATE t_questionnaire SET deleted = 1, update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
