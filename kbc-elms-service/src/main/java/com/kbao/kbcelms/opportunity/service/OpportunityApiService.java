package com.kbao.kbcelms.opportunity.service;

import cn.hutool.core.collection.CollectionUtil;import cn.hutool.json.JSON;import cn.hutool.json.JSONObject;import com.github.pagehelper.PageHelper;import com.github.pagehelper.PageInfo;import com.kbao.commons.exception.BusinessException;import com.kbao.commons.snowflake.IdWorker;import com.kbao.commons.web.PageRequest;import com.kbao.kbcbpm.process.vo.ActivityInfoVO;import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcelms.common.enums.OpportunityTypeEnum;import com.kbao.kbcelms.enums.OpportunityStatusEnum;import com.kbao.kbcelms.formConfig.model.FormConfigField;import com.kbao.kbcelms.formConfig.service.FormConfigService;import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;import com.kbao.kbcelms.opportunity.dao.OpportunityMapper;import com.kbao.kbcelms.opportunity.entity.Opportunity;import com.kbao.kbcelms.opportunity.model.OpportunityInsureInfo;import com.kbao.kbcelms.opportunity.vo.*;import com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail;import com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService;import com.kbao.kbcelms.opportunitydetail.vo.OpportunityDetailAddVo;import com.kbao.kbcelms.opportunitydetail.vo.OpportunityDetailVO;import com.kbao.kbcelms.opportunityfiles.model.OpportunityFiles;import com.kbao.kbcelms.opportunityfiles.service.OpportunityFilesService;import com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService;import com.kbao.kbcelms.enterpriseconfirmation.service.EnterpriseConfirmationService;import com.kbao.kbcelms.enterprise.base.service.EnterpriseBasicInfoService;import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;import com.kbao.kbcucs.context.RequestContext;import com.kbao.kbcucs.user.model.RequestInfo;import com.kbao.kbcucs.user.model.UserInfoResp;import com.kbao.tool.util.EmptyUtils;import com.kbao.tool.util.IDUtils;import org.springframework.beans.BeanUtils;import org.springframework.beans.factory.annotation.Autowired;import org.springframework.stereotype.Service;import org.springframework.transaction.annotation.Transactional;import org.springframework.util.CollectionUtils;import java.util.*;import java.util.stream.Collectors;
@Service
public class OpportunityApiService {
    @Autowired
    private OpportunityMapper opportunityMapper;
    @Autowired
    private OpportunityDetailService opportunityDetailService;
    @Autowired
    private OpportunityInsureInfoService opportunityInsureInfoService;
    @Autowired
    private FormConfigService formConfigService;
    @Autowired
    private OpportunityFilesService opportunityFilesService;
    @Autowired
    private OpportunityProcessService opportunityProcessService;
    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;
    @Autowired
    private OpportunityService opportunityService;
    @Autowired
    private EnterpriseConfirmationService enterpriseConfirmationService;
    @Autowired
    private EnterpriseBasicInfoService enterpriseBasicInfoService;

    public OpportunityConfigResVo getOpportunityConfigs(Integer opportunityId) {
        OpportunityConfigResVo configs = formConfigService.getOpportunityConfigs();
        if (opportunityId != null) {
            String insureTypes = opportunityMapper.getEmployeeInsureTypes(opportunityId);
            if (EmptyUtils.isNotEmpty(insureTypes)) {
                List<Integer> insureTypeIds = Arrays.stream(insureTypes.split(",")).map(Integer::parseInt)
                    .collect(Collectors.toList());
                List<FormConfigField> fields = formConfigService.getInsureFields(insureTypeIds);
                configs.setInsureFields(fields);
            }
        }
        return configs;
    }

    @Transactional(rollbackFor = Exception.class)
    public void add(OpportunityAddReqVo reqVo) {
        UserInfoResp userInfo = RequestContext.UserInfoResp.get();
        Opportunity oppo = new Opportunity();
        oppo.setOpportunityName(reqVo.getOpportunityName());
        oppo.setAgentEnterpriseId(reqVo.getAgentEnterpriseId());
        oppo.setOpportunityType(reqVo.getOpportunityType());
        oppo.setStatus(reqVo.getStatus());
        // 补充创建人信息
        oppo.setBizCode(IDUtils.generateIds("P"));
        oppo.setAgentCode(userInfo.getAgentCode());
        oppo.setAgentName(userInfo.getAgentName());
        oppo.setAreaCenterCode(userInfo.getAreaCenterCode());
        oppo.setAreaCenterName(userInfo.getAreaCenterName());
        oppo.setLegalCode(userInfo.getLegalCode());
        oppo.setLegalName(userInfo.getLegalName());
        oppo.setCompanyCode(userInfo.getCompanyCode());
        oppo.setCompanyName(userInfo.getCompanyName());
        oppo.setTradingCenterCode(userInfo.getTradingCenterCode());
        oppo.setTradingCenterName(userInfo.getTradingCenterName());
        oppo.setSalesCenterCode(userInfo.getSalesCenterCode());
        oppo.setSalesCenterName(userInfo.getSalesCenterName());
        oppo.setCreateId(userInfo.getId());
        oppo.setTenantId(userInfo.getTenantId());
        opportunityMapper.insert(oppo);
        // 保存企业明细信息
        OpportunityDetail detail = new OpportunityDetail();
        detail.setOpportunityId(oppo.getId());
        boolean isSubmit = OpportunityStatusEnum.SUBMITTED.getCode().equals(oppo.getStatus());
        opportunityDetailService.saveOpportunityDetail(reqVo, detail, isSubmit);

        if (OpportunityTypeEnum.EMPLOYEE_WELFARE.getCode().equals(reqVo.getOpportunityType())) {
            OpportunityInsureInfo insureInfo = new OpportunityInsureInfo();
            insureInfo.setOpportunityId(oppo.getId());
            insureInfo.setContent(reqVo.getInsureInfo());
            insureInfo.setCreateTime(new Date());
            opportunityInsureInfoService.save(insureInfo);
        }
        // 新增：企业确权逻辑
        GenAgentEnterprise enterprise = genAgentEnterpriseService.selectByPrimaryKey(oppo.getAgentEnterpriseId());
        if (enterprise != null) {
            // 获取企业基本信息
            EnterpriseBasicInfo basicInfo = enterpriseBasicInfoService.findByCreditCode(enterprise.getCreditCode());
            // 插入企业确权记录
            enterpriseConfirmationService.insertConfirmationRecord(basicInfo);
            if (isSubmit) {
                opportunityProcessService.startProcess(oppo.getId(), enterprise.getDtType());
            }
        }
    }

    public OpportunityAddReqVo getOpportunityDetail(Integer id) {
        OpportunityAddReqVo result = new OpportunityAddReqVo();
        String tenantId = ElmsContext.getTenantId();
        Opportunity oppo = opportunityMapper.selectByPrimaryKey(id);
        result.setAgentEnterpriseId(oppo.getAgentEnterpriseId());
        result.setOpportunityName(oppo.getOpportunityName());
        result.setOpportunityType(oppo.getOpportunityType());
        result.setStatus(oppo.getStatus());

        OpportunityDetail detail = opportunityDetailService.selectByOpportunityId(id, tenantId);
        String insureTypes = detail.getGeneralInsuranceType();
        if (OpportunityTypeEnum.EMPLOYEE_WELFARE.getCode().equals(oppo.getOpportunityType())) {
            insureTypes = detail.getEmployeeInsuranceType();

            OpportunityInsureInfo insureInfo = opportunityInsureInfoService.getByOpportunityId(id);
            result.setInsureInfo(insureInfo.getContent());
        }
        if (EmptyUtils.isNotEmpty(insureTypes)) {
            detail.setInsureTypes(Arrays.asList(insureTypes.split( ",")));
        }
        OpportunityDetailAddVo detailAddVo = new OpportunityDetailAddVo();
        BeanUtils.copyProperties(detail, detailAddVo);
        result.setDetail(detailAddVo);
        return result;
    }

    public OpportunityFullInfoVo getOpportunityFullInfo(Integer opportunityId) {
        OpportunityFullInfoVo fullInfo = (OpportunityFullInfoVo)this.getOpportunityDetail(opportunityId);
        List<OpportunityFiles> opportunityFiles = opportunityFilesService.findByOpportunityId(opportunityId);
        fullInfo.setFileList(opportunityFiles);
        List<ActivityInfoVO> progressList = opportunityService.getProcessProgress(opportunityId);
        if (CollectionUtil.isNotEmpty(progressList)) {
            List<OpportunityFullInfoVo.ActivityInfo> copyProgressList = new ArrayList<>();
            progressList.forEach(a -> {
                OpportunityFullInfoVo.ActivityInfo progress = new OpportunityFullInfoVo.ActivityInfo();
                BeanUtils.copyProperties(a, progress);
                copyProgressList.add(progress);
            });
            fullInfo.setProgressList(copyProgressList);
        }
        return fullInfo;
    }

    public void updateOpportunity(OpportunityAddReqVo reqVo) {
        Opportunity oppo = opportunityMapper.selectByPrimaryKey(reqVo.getOpportunityId());
        if (oppo.getStatus() > 0) {
            throw new BusinessException("机会提交后不允许修改");
        }
        boolean isSubmit = false;
        if (OpportunityStatusEnum.PENDING_SUBMIT.getCode().equals(oppo.getStatus())
            && OpportunityStatusEnum.SUBMITTED.getCode().equals(reqVo.getStatus())) {
            isSubmit = true;
        }
        oppo.setOpportunityName(reqVo.getOpportunityName());
        oppo.setStatus(reqVo.getStatus());
        oppo.setUpdateTime(new Date());
        oppo.setUpdateId(ElmsContext.getUser().getUserId());
        opportunityMapper.updateByPrimaryKey(oppo);

        String tenantId = ElmsContext.getTenantId();
        OpportunityDetail detail = opportunityDetailService.selectByOpportunityId(reqVo.getOpportunityId(), tenantId);
        opportunityDetailService.saveOpportunityDetail(reqVo, detail, isSubmit);

        if (OpportunityTypeEnum.EMPLOYEE_WELFARE.getCode().equals(oppo.getOpportunityType())) {
            opportunityInsureInfoService.updateInsureInfo(oppo.getId(), reqVo.getInsureInfo());
        }
        if (isSubmit) {
            GenAgentEnterprise enterprise = genAgentEnterpriseService.selectByPrimaryKey(oppo.getAgentEnterpriseId());
            opportunityProcessService.startProcess(oppo.getId(), enterprise.getDtType());
        }
    }

    public PageInfo<OpportunityListResVo> getAgentOpportunityList(PageRequest<OpportunitySearchReqVo> pageRequest) {
        // todo 补充锁定逻辑
        OpportunitySearchReqVo param = pageRequest.getParam();
        param.setAgentCode(ElmsContext.getUser().getAgentCode());
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<OpportunityListResVo> list = opportunityMapper.getAgentOpportunityList(param);
        if (EmptyUtils.isNotEmpty(list)) {
            Map<Integer,String> insureTypeMap = formConfigService.getInsureTypeMap();
            list.forEach(item -> {
                if (OpportunityTypeEnum.EMPLOYEE_WELFARE.getCode().equals(item.getOpportunityType())) {
                    String insuranceType = item.getInsureTypes();
                    if (EmptyUtils.isNotEmpty(insuranceType)) {
                        String insuranceTypeName = Arrays.stream(insuranceType.split(",")).map(id -> insureTypeMap.get(Integer.parseInt(id)))
                            .collect(Collectors.joining(","));
                        item.setInsureTypes(insuranceTypeName);
                    }
                }
            });
        }
        return new PageInfo<>(list);
    }

    public void submit(Integer opportunityId) {
        Opportunity oppo = opportunityMapper.selectByPrimaryKey(opportunityId);
        if (oppo.getStatus() > 0) {
            throw new BusinessException("机会已提交");
        }
        opportunityMapper.submitOpportunity(opportunityId);
        opportunityDetailService.updateSubmitTime(opportunityId);

        GenAgentEnterprise enterprise = genAgentEnterpriseService.selectByPrimaryKey(oppo.getAgentEnterpriseId());
        opportunityProcessService.startProcess(oppo.getId(), enterprise.getDtType());
    }

    public void delete(Integer opportunityId) {
        Opportunity oppo = opportunityMapper.selectByPrimaryKey(opportunityId);
        if (oppo.getStatus() > 0) {
            throw new BusinessException("机会已提交，无法删除");
        }
        opportunityMapper.deleteByPrimaryKey(opportunityId);
    }

    public boolean getSubmitOpportunityNum(Integer enterpriseId) {
        return opportunityMapper.getSubmitOpportunityNum(enterpriseId) > 0;
    }
}
