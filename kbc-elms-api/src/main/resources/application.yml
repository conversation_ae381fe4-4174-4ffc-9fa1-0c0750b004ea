spring:
  profiles:
    include: @profiles.active@
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  main:
    allow-bean-definition-overriding: true

############## mybatis 配置 ################33
mybatis:
  mapper-locations: classpath*:/com/kbao/kbcelms/**/entity/*Mapper.xml


#开启json压缩
server:
  compression:
    enabled: true
    mime-types: application/json
    min-response-size: 20480
  tomcat:
    uri-encoding: UTF-8
    max-threads: 300
    max-connections: 1000
  # 单位 KB
  max-http-header-size: 1000000

pagehelper:
  helper-dialect: mysql
  reasonable: false
  support-methods-arguments: true
  params: count=countSql
  offset-as-page-num: false

##feign参数优化
feign:
  client:
    config:
      default:
        # 连接其它项目服务超时时间
        connectTimeout: 5000
        # 读取其它项目服务超时时间
        readTimeout: 30000

##应用配置
app:
  code: mesApp

flowable:
  history-level: audit
  database-schema-update: true
  async-executor-activate: false
  idm:
    enabled: false