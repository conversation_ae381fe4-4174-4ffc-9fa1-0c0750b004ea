import http from "@/utils/httpService";
import { rootPath } from '@/utils/globalParam';

/**
 * 线上产品配置管理 API
 */

// 分页查询线上产品配置列表
export const getOnlineProductConfig = (pageRequest) => {
  return http.Axios.post(rootPath + "/api/elms/onlineProduct/page", pageRequest);
};

// 获取行业树形选项
export const getIndustryTree = (data) => {
  return http.Axios.post(rootPath + "/api/industry/tree", data);
};

// 获取线上产品配置详情
export const getOnlineProductConfigDetail = (id) => {
  return http.Axios.get(rootPath + `/api/elms/onlineProduct/${id}`);
};

// 创建线上产品配置
export const createOnlineProductConfig = (data) => {
  return http.Axios.post(rootPath + "/api/elms/onlineProduct/add", data);
};

// 更新线上产品配置
export const updateOnlineProductConfig = (data) => {
  return http.Axios.post(rootPath + "/api/elms/onlineProduct/update", data);
};

// 删除线上产品配置
export const deleteOnlineProductConfig = (id) => {
  return http.Axios.post(rootPath + `/api/elms/onlineProduct/delete/${id}`);
};

// 获取险种类型选项
export const getInsuranceTypeOptions = () => {
  return http.Axios.get(rootPath + "/api/elms/onlineProduct/insuranceTypeOptions");
};

// 检查配置是否存在
export const checkConfigExists = (industryCode, probability, impact, level, id) => {
  const params = new URLSearchParams();
  params.append('industryCode', industryCode);
  params.append('probability', probability);
  params.append('impact', impact);
  params.append('level', level);
  if (id) {
    params.append('id', id);
  }
  return http.Axios.get(rootPath + `/api/elms/onlineProduct/checkConfig?${params.toString()}`);
};

// 兼容旧接口的方法（保持向后兼容）
export const saveIndustryConfig = (industryCode, configs) => {
  // 如果是单个配置，直接保存
  if (configs && configs.length === 1) {
    const config = configs[0];
    if (config.id) {
      return updateOnlineProductConfig({
        ...config,
        industryCode: industryCode
      });
    } else {
      return createOnlineProductConfig({
        ...config,
        industryCode: industryCode
      });
    }
  }
  
  // 多个配置的情况，需要逐个处理
  const promises = configs.map(config => {
    if (config.id) {
      return updateOnlineProductConfig({
        ...config,
        industryCode: industryCode
      });
    } else {
      return createOnlineProductConfig({
        ...config,
        industryCode: industryCode
      });
    }
  });
  
  return Promise.all(promises);
}; 