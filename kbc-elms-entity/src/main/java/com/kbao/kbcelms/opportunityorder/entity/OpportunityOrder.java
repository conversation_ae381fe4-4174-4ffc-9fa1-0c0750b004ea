package com.kbao.kbcelms.opportunityorder.entity;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.Date;
import javax.validation.constraints.Size;

/**
 * 机会订单
 */
@Data
@ApiModel(description = "机会订单实体类")
public class OpportunityOrder {
    /** 编号 */
    private Integer id;
    /** 机会id */
    @NotBlank(message = "机会id不能为空")
    private String opportunityId;
    /** 租户id */
    private String tenantId;
    /** 公司编码 */
    @Size(max = 16, message = "公司编码长度不能超过16个字符")
    private String companyCode;
    /** 公司名称 */
    private String companyName;
    /** 保单号 */  // 新增字段注释
    @Size(max = 16, message = "保单号长度不能超过16个字符")
    private String policyNo;
    /** 订单号 */
    @Size(max = 16, message = "订单号长度不能超过16个字符")
    private String orderCode;
    /** 创建人编号 */
    private String createId;
    /** 创建时间 */
    private Date createTime;
    /** 更新人编号 */
    private String updateId;
    /** 更新时间 */
    private Date updateTime;
    /** 是否删除 0 未删除  1已删除 */
    private Integer isDeleted;
}