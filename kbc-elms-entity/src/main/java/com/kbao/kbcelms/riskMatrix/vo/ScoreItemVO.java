package com.kbao.kbcelms.riskMatrix.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评分项视图对象
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScoreItemVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 评分项名称
     */
    private String name;
    
    /**
     * 评分项编码
     */
    private String code;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 所属类别
     */
    private String category;
    
    /**
     * 权重
     */
    private Double weight;
    
    /**
     * 最大分值
     */
    private Integer maxScore;

    /**
     * 是否关联公式：0-否，1-是
     */
    private Integer isFormula;

    /**
     * 关联公式ID
     */
    private Long formulaId;
    
    /**
     * 公式名称
     */
    private String formulaName;
    
    /**
     * 系数
     */
    private Double coefficient;
    
    /**
     * 适用企业类型列表
     */
    private List<String> enterpriseTypes;
    
    /**
     * 企业类型名称（用于显示）
     */
    private String enterpriseTypesDisplay;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    private String statusName;
    
    /**
     * 评分标准列表
     */
    private List<CriteriaVO> criteria;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 更新人
     */
    private String updateUser;
    
    /**
     * 评分标准视图对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CriteriaVO {
        
        /**
         * 标准ID
         */
        private Long id;
        
        /**
         * 分值
         */
        private Integer score;
        
        /**
         * 评分标准描述
         */
        private String description;
        
        /**
         * 排序序号
         */
        private Integer sortOrder;
    }
}
