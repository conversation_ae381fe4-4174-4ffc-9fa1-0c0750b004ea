package com.kbao.kbcelms.questionnaire.entity;

import com.kbao.kbcelms.questionnaire.enums.QuestionnaireStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 问卷实体类
 */
@Data
public class Questionnaire implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问卷ID
     */
    private Long id;

    /**
     * 问卷标题
     */
    private String title;

    /**
     * 问卷描述
     */
    private String description;

    /**
     * 适用企业类型，多个用逗号分隔
     */
    private String enterpriseTypes;

    /**
     * 状态：0-禁用，1-启用，2-草稿
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新用户
     */
    private String updateUser;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;

    /**
     * 问题列表（非数据库字段）
     */
    private List<QuestionnaireQuestion> questions;

    /**
     * 问题数量（非数据库字段）
     */
    private Integer questionCount;

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        QuestionnaireStatusEnum statusEnum = QuestionnaireStatusEnum.getByValue(this.status);
        return statusEnum != null ? statusEnum.getDesc() : "未知";
    }

    /**
     * 获取企业类型列表
     */
    public List<String> getEnterpriseTypeList() {
        if (this.enterpriseTypes == null || this.enterpriseTypes.trim().isEmpty()) {
            return Arrays.asList();
        }
        return Arrays.asList(this.enterpriseTypes.split(","));
    }
}
