<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.industrylimit.dao.IndustryLimitActionMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.industrylimit.entity.IndustryLimitAction">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="rule_id" property="ruleId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="service_ids" property="serviceIds" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, rule_id, type, service_ids, description, sort_order, create_time, tenant_id
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_industry_limit_action
        WHERE id = #{id}
    </select>

    <!-- 根据规则ID查询执行动作列表 -->
    <select id="selectByRuleId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_industry_limit_action
        WHERE rule_id = #{ruleId}
        ORDER BY sort_order ASC
    </select>

    <!-- 根据参数查询 -->
    <select id="selectByParam" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_industry_limit_action
        <where>
            <if test="param != null">
                <if test="param.ruleId != null">
                    AND rule_id = #{param.ruleId}
                </if>
                <if test="param.type != null and param.type != ''">
                    AND type = #{param.type}
                </if>
            </if>
        </where>
        ORDER BY sort_order ASC
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.kbao.kbcelms.industrylimit.entity.IndustryLimitAction" 
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_industry_limit_action (
            rule_id, type, service_ids, description, sort_order, create_time, tenant_id
        ) VALUES (
            #{ruleId}, #{type}, #{serviceIds}, #{description}, #{sortOrder}, #{createTime}, #{tenantId}
        )
    </insert>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.industrylimit.entity.IndustryLimitAction">
        UPDATE t_industry_limit_action
        <set>
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="serviceIds != null">service_ids = #{serviceIds},</if>
            <if test="description != null">description = #{description},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据规则ID删除执行动作 -->
    <delete id="deleteByRuleId" parameterType="java.lang.Long">
        DELETE FROM t_industry_limit_action WHERE rule_id = #{ruleId}
    </delete>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM t_industry_limit_action WHERE id = #{id}
    </delete>

</mapper>
